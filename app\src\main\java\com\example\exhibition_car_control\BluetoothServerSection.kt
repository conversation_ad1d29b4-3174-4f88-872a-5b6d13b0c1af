package com.example.exhibition_car_control

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zerosense.bluetooth.BluetoothManager
import java.text.SimpleDateFormat
import java.util.*

data class ReceivedMessage(
    val senderAddress: String,
    val data: String,
    val timestamp: Long = System.currentTimeMillis()
)

@Composable
fun BluetoothServerSection(
    bluetoothManager: BluetoothManager?,
    isServerRunning: Boolean,
    receivedMessages: List<ReceivedMessage>,
    onStartServer: () -> Unit,
    onStopServer: () -> Unit,
    onClearMessages: () -> Unit
) {
    val listState = rememberLazyListState()
    
    // 自动滚动到最新消息
    LaunchedEffect(receivedMessages.size) {
        if (receivedMessages.isNotEmpty()) {
            listState.animateScrollToItem(receivedMessages.size - 1)
        }
    }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 标题和控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "蓝牙服务器（接收方）",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    if (isServerRunning) {
                        Button(
                            onClick = onStopServer,
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                        ) {
                            Text("停止服务器", fontSize = 12.sp)
                        }
                    } else {
                        Button(
                            onClick = onStartServer,
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Green)
                        ) {
                            Text("启动服务器", fontSize = 12.sp)
                        }
                    }
                    
                    if (receivedMessages.isNotEmpty()) {
                        Button(
                            onClick = onClearMessages,
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Gray)
                        ) {
                            Text("清空", fontSize = 12.sp)
                        }
                    }
                }
            }
            
            // 服务器状态指示器
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .padding(2.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .padding(1.dp)
                    ) {
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = if (isServerRunning) Color.Green else Color.Gray
                            ),
                            modifier = Modifier.fillMaxSize()
                        ) {}
                    }
                }

                Text(
                    text = if (isServerRunning) "服务器运行中，等待连接..." else "服务器已停止",
                    fontSize = 14.sp,
                    color = if (isServerRunning) Color.Green else Color.Gray
                )
            }

            // 蓝牙广播状态（仅树莓派版本显示）
            if (com.example.exhibition_car_control.config.DeviceConfig.isRaspberryPiVersion && isServerRunning) {
                val isBroadcasting = bluetoothManager?.isBroadcasting() ?: false
                val currentBluetoothName = bluetoothManager?.getCurrentBluetoothName()

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .padding(2.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .padding(1.dp)
                        ) {
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = if (isBroadcasting) Color(0xFF2196F3) else Color.Gray
                                ),
                                modifier = Modifier.fillMaxSize()
                            ) {}
                        }
                    }

                    Column {
                        Text(
                            text = if (isBroadcasting) "蓝牙广播：$currentBluetoothName" else "蓝牙广播已停止",
                            fontSize = 14.sp,
                            color = if (isBroadcasting) Color(0xFF2196F3) else Color.Gray
                        )
                        if (isBroadcasting) {
                            Text(
                                text = "手机可搜索到此设备名称进行连接",
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
            
            // 消息统计
            if (receivedMessages.isNotEmpty()) {
                Text(
                    text = "已接收 ${receivedMessages.size} 条消息",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
            
            // 接收到的消息列表
            if (receivedMessages.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.Black.copy(alpha = 0.05f))
                ) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(8.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        items(receivedMessages) { message ->
                            MessageItem(message = message)
                        }
                    }
                }
            } else if (isServerRunning) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFF1976D2).copy(alpha = 0.1f))
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "等待接收消息...",
                            fontSize = 14.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                    }
                }
            }
            
            // 使用说明
            if (!isServerRunning) {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color(0xFF1976D2).copy(alpha = 0.1f))
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Text(
                            text = "使用说明：",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                        Text(
                            text = "1. 点击\"启动服务器\"开始监听连接",
                            fontSize = 11.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                        Text(
                            text = "2. 在另一台设备上配对并发送数据",
                            fontSize = 11.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                        Text(
                            text = "3. 接收到的消息会显示在上方列表中",
                            fontSize = 11.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MessageItem(message: ReceivedMessage) {
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    val timeString = timeFormat.format(Date(message.timestamp))
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(8.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "来自: ${message.senderAddress}",
                    fontSize = 10.sp,
                    color = Color.Gray
                )
                Text(
                    text = timeString,
                    fontSize = 10.sp,
                    color = Color.Gray
                )
            }
            Text(
                text = message.data,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
