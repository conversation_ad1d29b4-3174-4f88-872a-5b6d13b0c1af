package com.example.exhibition_car_control

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zerosense.bluetooth.BluetoothManager
import com.example.exhibition_car_control.config.DeviceConfig
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

data class ReceivedMessage(
    val senderAddress: String,
    val data: String,
    val timestamp: Long = System.currentTimeMillis()
)

// 树莓派控制中心相关数据结构
data class RaspberryPiScanCommand(
    val command: String = "SCAN_BLE_DEVICES",
    val devicePrefixes: List<String> = listOf(
        DeviceConfig.BluetoothDevicePrefix.bluetoothKey,    // Dotix - 蓝牙按键
        DeviceConfig.BluetoothDevicePrefix.charger,         // SMART-WC - 充电器
        DeviceConfig.BluetoothDevicePrefix.mainLight,       // SMART-ML - 主灯
        DeviceConfig.BluetoothDevicePrefix.ambientLight,    // SMART-AL - 氛围灯
        DeviceConfig.BluetoothDevicePrefix.aromatherapy,    // SMART-DF - 香薰
        DeviceConfig.BluetoothDevicePrefix.fan              // SMART-FN - 风扇
    ), // 要过滤的设备前缀列表
    val scanDuration: Int = 10 // 扫描持续时间（秒）
) {
    fun toJsonString(): String {
        val prefixesJson = devicePrefixes.joinToString(",") { "\"$it\"" }
        return """{"command":"$command","devicePrefixes":[$prefixesJson],"scanDuration":$scanDuration}"""
    }
}

data class RaspberryPiConnectCommand(
    val command: String = "CONNECT_BLE_DEVICE",
    val deviceAddress: String,
    val deviceName: String? = null
) {
    fun toJsonString(): String {
        return """{"command":"$command","deviceAddress":"$deviceAddress","deviceName":"${deviceName ?: ""}"}"""
    }
}



data class RaspberryPiScanResult(
    val status: String, // "SUCCESS", "ERROR", "SCANNING"
    val message: String,
    val devices: List<ScannedBleDevice> = emptyList(),
    val timestamp: Long = System.currentTimeMillis()
) {
    companion object {
        fun fromJsonString(jsonString: String): RaspberryPiScanResult? {
            return try {
                // 简单的JSON解析，实际项目中应该使用Gson或其他JSON库
                when {
                    jsonString.contains("\"status\":\"SUCCESS\"") -> {
                        // 解析设备列表
                        val devices = parseDevicesFromJson(jsonString)
                        RaspberryPiScanResult("SUCCESS", "扫描完成", devices)
                    }
                    jsonString.contains("\"status\":\"SCANNING\"") -> {
                        RaspberryPiScanResult("SCANNING", "正在扫描...", emptyList())
                    }
                    jsonString.contains("\"status\":\"ERROR\"") -> {
                        val message = extractMessageFromJson(jsonString) ?: "扫描失败"
                        RaspberryPiScanResult("ERROR", message, emptyList())
                    }
                    else -> null
                }
            } catch (e: Exception) {
                null
            }
        }

        private fun parseDevicesFromJson(jsonString: String): List<ScannedBleDevice> {
            // 简化的JSON解析，实际应该使用专业的JSON库
            val devices = mutableListOf<ScannedBleDevice>()
            try {
                // 这里应该实现完整的JSON解析
                // 暂时返回空列表，后续可以完善
            } catch (e: Exception) {
                // 解析失败
            }
            return devices
        }

        private fun extractMessageFromJson(jsonString: String): String? {
            return try {
                // 简化的消息提取
                val messageStart = jsonString.indexOf("\"message\":\"") + 11
                val messageEnd = jsonString.indexOf("\"", messageStart)
                if (messageStart > 10 && messageEnd > messageStart) {
                    jsonString.substring(messageStart, messageEnd)
                } else null
            } catch (e: Exception) {
                null
            }
        }
    }
}

enum class RaspberryPiScanState {
    IDLE,           // 未开始扫描
    SCANNING,       // 正在扫描
    COMPLETED,      // 扫描完成
    ERROR           // 扫描出错
}

/**
 * Android端BLE扫描处理器
 * 用于处理来自手机端的扫描指令，执行真正的BLE扫描
 */
class AndroidBLEScanHandler(
    private val context: Context,
    private val bluetoothManager: BluetoothManager
) {
    private val TAG = "AndroidBLEScanHandler"
    private var isScanning = false
    private var bleScanner: BluetoothLeScanner? = null
    private val handler = Handler(Looper.getMainLooper())
    private var scanCallback: ScanCallback? = null

    // 配置的设备前缀
    private val devicePrefixes = listOf(
        DeviceConfig.BluetoothDevicePrefix.bluetoothKey,    // Dotix
        DeviceConfig.BluetoothDevicePrefix.charger,         // SMART-WC
        DeviceConfig.BluetoothDevicePrefix.mainLight,       // SMART-ML
        DeviceConfig.BluetoothDevicePrefix.ambientLight,    // SMART-AL
        DeviceConfig.BluetoothDevicePrefix.aromatherapy,    // SMART-DF
        DeviceConfig.BluetoothDevicePrefix.fan              // SMART-FN
    )

    /**
     * 处理来自手机的蓝牙消息
     */
    fun handleBluetoothMessage(senderAddress: String, message: String): Boolean {
        Log.d(TAG, "收到消息来自 $senderAddress: $message")

        return try {
            // 解析JSON指令
            when {
                message.contains("\"command\":\"SCAN_BLE_DEVICES\"") -> {
                    handleScanCommand(senderAddress, message)
                }
                message.contains("\"command\":\"STOP_SCAN\"") -> {
                    handleStopScanCommand(senderAddress)
                }
                else -> {
                    Log.w(TAG, "未知指令: $message")
                    sendErrorResponse(senderAddress, "未知指令")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理指令时出错", e)
            sendErrorResponse(senderAddress, "处理指令时出错: ${e.message}")
        }
    }

    /**
     * 处理扫描指令
     */
    private fun handleScanCommand(senderAddress: String, message: String): Boolean {
        if (isScanning) {
            Log.w(TAG, "扫描已在进行中")
            return sendErrorResponse(senderAddress, "扫描已在进行中")
        }

        // 解析扫描参数
        val scanDuration = extractScanDuration(message) ?: 10
        val prefixes = extractDevicePrefixes(message) ?: devicePrefixes

        Log.i(TAG, "开始BLE扫描 - 前缀: $prefixes, 持续时间: ${scanDuration}秒")

        // 发送扫描开始响应
        val prefixesStr = prefixes.joinToString(", ")
        sendResponse(senderAddress, RaspberryPiScanResult(
            status = "SCANNING",
            message = "开始扫描BLE设备（过滤前缀: $prefixesStr）...",
            devices = emptyList()
        ))

        // 启动BLE扫描
        return startBLEScan(senderAddress, prefixes, scanDuration)
    }

    /**
     * 启动BLE扫描
     */
    private fun startBLEScan(senderAddress: String, prefixes: List<String>, duration: Int): Boolean {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled) {
            Log.e(TAG, "蓝牙适配器不可用")
            return sendErrorResponse(senderAddress, "蓝牙适配器不可用")
        }

        bleScanner = bluetoothAdapter.bluetoothLeScanner
        if (bleScanner == null) {
            Log.e(TAG, "BLE扫描器不可用")
            return sendErrorResponse(senderAddress, "BLE扫描器不可用")
        }

        val discoveredDevices = mutableListOf<ScannedBleDevice>()

        scanCallback = object : ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                val device = result.device
                val deviceName = device.name ?: "Unknown"
                val deviceAddress = device.address
                val rssi = result.rssi

                Log.d(TAG, "发现设备: $deviceName ($deviceAddress) RSSI: $rssi")

                // 检查是否匹配任何前缀
                for (prefix in prefixes) {
                    if (deviceName.startsWith(prefix)) {
                        Log.i(TAG, "匹配前缀 '$prefix': $deviceName")

                        // 避免重复添加
                        if (discoveredDevices.none { it.address == deviceAddress }) {
                            discoveredDevices.add(ScannedBleDevice(
                                name = deviceName,
                                address = deviceAddress,
                                rssi = rssi,
                                isConnectable = true
                            ))
                        }
                        break
                    }
                }
            }

            override fun onScanFailed(errorCode: Int) {
                Log.e(TAG, "BLE扫描失败，错误码: $errorCode")
                isScanning = false
                sendErrorResponse(senderAddress, "BLE扫描失败，错误码: $errorCode")
            }
        }

        // 开始扫描
        try {
            val scanSettings = ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                .build()

            bleScanner?.startScan(null, scanSettings, scanCallback)
            isScanning = true

            Log.i(TAG, "BLE扫描已启动，持续时间: ${duration}秒")

            // 设置扫描超时
            handler.postDelayed({
                stopBLEScan()

                Log.i(TAG, "扫描完成，发现 ${discoveredDevices.size} 个匹配设备")

                // 发送扫描结果
                sendResponse(senderAddress, RaspberryPiScanResult(
                    status = "SUCCESS",
                    message = "扫描完成，发现${discoveredDevices.size}个符合条件的设备",
                    devices = discoveredDevices.toList()
                ))
            }, (duration * 1000).toLong())

            return true

        } catch (e: SecurityException) {
            Log.e(TAG, "缺少蓝牙权限", e)
            isScanning = false
            return sendErrorResponse(senderAddress, "缺少蓝牙权限")
        } catch (e: Exception) {
            Log.e(TAG, "启动BLE扫描失败", e)
            isScanning = false
            return sendErrorResponse(senderAddress, "启动BLE扫描失败: ${e.message}")
        }
    }

    /**
     * 停止BLE扫描
     */
    private fun stopBLEScan() {
        if (isScanning && bleScanner != null && scanCallback != null) {
            try {
                bleScanner?.stopScan(scanCallback)
                Log.i(TAG, "BLE扫描已停止")
            } catch (e: SecurityException) {
                Log.e(TAG, "停止扫描时缺少权限", e)
            } catch (e: Exception) {
                Log.e(TAG, "停止BLE扫描失败", e)
            }
        }
        isScanning = false
        scanCallback = null
    }

    /**
     * 处理停止扫描指令
     */
    private fun handleStopScanCommand(senderAddress: String): Boolean {
        if (!isScanning) {
            Log.w(TAG, "当前没有进行扫描")
            return sendErrorResponse(senderAddress, "当前没有进行扫描")
        }

        stopBLEScan()

        sendResponse(senderAddress, RaspberryPiScanResult(
            status = "SUCCESS",
            message = "扫描已停止",
            devices = emptyList()
        ))

        return true
    }

    /**
     * 发送响应到手机
     */
    private fun sendResponse(senderAddress: String, result: RaspberryPiScanResult) {
        val jsonResponse = buildJsonResponse(result)
        Log.d(TAG, "发送响应到 $senderAddress: $jsonResponse")

        try {
            bluetoothManager.sendData(senderAddress, jsonResponse)
        } catch (e: Exception) {
            Log.e(TAG, "发送响应失败", e)
        }
    }

    /**
     * 发送错误响应
     */
    private fun sendErrorResponse(senderAddress: String, errorMessage: String): Boolean {
        sendResponse(senderAddress, RaspberryPiScanResult(
            status = "ERROR",
            message = errorMessage,
            devices = emptyList()
        ))
        return false
    }

    /**
     * 构建JSON响应
     */
    private fun buildJsonResponse(result: RaspberryPiScanResult): String {
        val devicesJson = result.devices.joinToString(",") { device ->
            """{"name":"${device.name ?: ""}","address":"${device.address}","rssi":${device.rssi},"isConnectable":${device.isConnectable}}"""
        }

        return """{"status":"${result.status}","message":"${result.message}","devices":[$devicesJson]}"""
    }

    /**
     * 从JSON消息中提取扫描持续时间
     */
    private fun extractScanDuration(message: String): Int? {
        return try {
            val regex = """"scanDuration":(\d+)""".toRegex()
            regex.find(message)?.groupValues?.get(1)?.toInt()
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 从JSON消息中提取设备前缀列表
     */
    private fun extractDevicePrefixes(message: String): List<String>? {
        return try {
            val regex = """"devicePrefixes":\[(.*?)\]""".toRegex()
            val match = regex.find(message)
            if (match != null) {
                val prefixesStr = match.groupValues[1]
                prefixesStr.split(",")
                    .map { it.trim().removeSurrounding("\"") }
                    .filter { it.isNotEmpty() }
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
}

@Composable
fun BluetoothServerSection(
    bluetoothManager: BluetoothManager?,
    isServerRunning: Boolean,
    receivedMessages: List<ReceivedMessage>,
    onStartServer: () -> Unit,
    onStopServer: () -> Unit,
    onClearMessages: () -> Unit
) {
    val context = LocalContext.current
    val listState = rememberLazyListState()

    // 创建BLE扫描处理器
    val bleScanHandler = remember(bluetoothManager) {
        bluetoothManager?.let { AndroidBLEScanHandler(context, it) }
    }

    // 处理接收到的消息，检查是否为扫描指令
    LaunchedEffect(receivedMessages.size) {
        if (receivedMessages.isNotEmpty() && bleScanHandler != null) {
            val latestMessage = receivedMessages.last()

            // 检查是否为BLE扫描相关指令
            if (latestMessage.data.contains("SCAN_BLE_DEVICES") ||
                latestMessage.data.contains("STOP_SCAN")) {

                Log.d("BluetoothServerSection", "处理BLE扫描指令: ${latestMessage.data}")
                bleScanHandler.handleBluetoothMessage(latestMessage.senderAddress, latestMessage.data)
            }
        }
    }

    // 自动滚动到最新消息
    LaunchedEffect(receivedMessages.size) {
        if (receivedMessages.isNotEmpty()) {
            listState.animateScrollToItem(receivedMessages.size - 1)
        }
    }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 标题和控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "蓝牙服务器（接收方）",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    if (isServerRunning) {
                        Button(
                            onClick = onStopServer,
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                        ) {
                            Text("停止服务器", fontSize = 12.sp)
                        }
                    } else {
                        Button(
                            onClick = onStartServer,
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Green)
                        ) {
                            Text("启动服务器", fontSize = 12.sp)
                        }
                    }
                    
                    if (receivedMessages.isNotEmpty()) {
                        Button(
                            onClick = onClearMessages,
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Gray)
                        ) {
                            Text("清空", fontSize = 12.sp)
                        }
                    }
                }
            }
            
            // 服务器状态指示器
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .padding(2.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .padding(1.dp)
                    ) {
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = if (isServerRunning) Color.Green else Color.Gray
                            ),
                            modifier = Modifier.fillMaxSize()
                        ) {}
                    }
                }
                
                Text(
                    text = if (isServerRunning) "服务器运行中，等待连接..." else "服务器已停止",
                    fontSize = 14.sp,
                    color = if (isServerRunning) Color.Green else Color.Gray
                )
            }
            
            // 消息统计
            if (receivedMessages.isNotEmpty()) {
                Text(
                    text = "已接收 ${receivedMessages.size} 条消息",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
            
            // 接收到的消息列表
            if (receivedMessages.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.Black.copy(alpha = 0.05f))
                ) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(8.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        items(receivedMessages) { message ->
                            MessageItem(message = message)
                        }
                    }
                }
            } else if (isServerRunning) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFF1976D2).copy(alpha = 0.1f))
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "等待接收消息...",
                            fontSize = 14.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                    }
                }
            }
            
            // 使用说明
            if (!isServerRunning) {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color(0xFF1976D2).copy(alpha = 0.1f))
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Text(
                            text = "使用说明：",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                        Text(
                            text = "1. 点击\"启动服务器\"开始监听连接",
                            fontSize = 11.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                        Text(
                            text = "2. 在另一台设备上配对并发送数据",
                            fontSize = 11.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                        Text(
                            text = "3. 接收到的消息会显示在上方列表中",
                            fontSize = 11.sp,
                            color = Color(0xFF1976D2) // 深蓝色，更清晰可见
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MessageItem(message: ReceivedMessage) {
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    val timeString = timeFormat.format(Date(message.timestamp))
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(8.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "来自: ${message.senderAddress}",
                    fontSize = 10.sp,
                    color = Color.Gray
                )
                Text(
                    text = timeString,
                    fontSize = 10.sp,
                    color = Color.Gray
                )
            }
            Text(
                text = message.data,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
