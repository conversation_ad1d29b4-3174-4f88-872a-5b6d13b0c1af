#!/bin/bash

# 蓝牙无感连接功能测试脚本

echo "=========================================="
echo "蓝牙无感连接功能测试脚本"
echo "=========================================="

PACKAGE_NAME="com.example.exhibition_car_control"

# 检查应用是否已安装
echo "正在检查应用安装状态..."
if ! pm list packages | grep -q "$PACKAGE_NAME"; then
    echo "❌ 应用未安装，请先安装应用"
    exit 1
fi

echo "✅ 应用已安装"

# 检查是否为系统应用
echo "正在检查系统应用状态..."
if pm list packages -s | grep -q "$PACKAGE_NAME"; then
    echo "✅ 应用已安装为系统应用"
    SYSTEM_APP=true
else
    echo "⚠️  应用未安装为系统应用，无感连接功能可能不可用"
    SYSTEM_APP=false
fi

# 检查权限状态
echo "正在检查权限状态..."
PERMISSIONS_OUTPUT=$(dumpsys package $PACKAGE_NAME | grep -A 20 "requested permissions:")

if echo "$PERMISSIONS_OUTPUT" | grep -q "android.permission.BLUETOOTH_PRIVILEGED"; then
    echo "✅ BLUETOOTH_PRIVILEGED权限已声明"
    
    # 检查权限是否已授予
    if echo "$PERMISSIONS_OUTPUT" | grep -q "android.permission.BLUETOOTH_PRIVILEGED.*granted=true"; then
        echo "✅ BLUETOOTH_PRIVILEGED权限已授予"
        PRIVILEGED_GRANTED=true
    else
        echo "⚠️  BLUETOOTH_PRIVILEGED权限未授予"
        PRIVILEGED_GRANTED=false
    fi
else
    echo "❌ BLUETOOTH_PRIVILEGED权限未声明"
    PRIVILEGED_GRANTED=false
fi

# 检查其他蓝牙权限
echo "正在检查其他蓝牙权限..."
BLUETOOTH_PERMISSIONS=(
    "android.permission.BLUETOOTH"
    "android.permission.BLUETOOTH_ADMIN"
    "android.permission.ACCESS_FINE_LOCATION"
    "android.permission.ACCESS_COARSE_LOCATION"
)

for permission in "${BLUETOOTH_PERMISSIONS[@]}"; do
    if echo "$PERMISSIONS_OUTPUT" | grep -q "$permission.*granted=true"; then
        echo "✅ $permission 已授予"
    else
        echo "⚠️  $permission 未授予或未声明"
    fi
done

# 检查蓝牙硬件状态
echo "正在检查蓝牙硬件状态..."
if dumpsys bluetooth_manager | grep -q "enabled: true"; then
    echo "✅ 蓝牙已启用"
else
    echo "⚠️  蓝牙未启用"
fi

# 生成测试报告
echo ""
echo "=========================================="
echo "测试报告"
echo "=========================================="

if [ "$SYSTEM_APP" = true ] && [ "$PRIVILEGED_GRANTED" = true ]; then
    echo "🎉 无感连接功能完全可用！"
    echo ""
    echo "功能状态："
    echo "  ✅ 系统应用安装"
    echo "  ✅ 特权权限获得"
    echo "  ✅ 支持自动配对"
    echo "  ✅ 支持批量连接"
    echo ""
    echo "建议测试步骤："
    echo "  1. 启动应用"
    echo "  2. 查看'静默连接控制'区域应显示绿色提示"
    echo "  3. 扫描附近设备"
    echo "  4. 点击'无感配对'按钮测试自动配对"
    echo "  5. 使用'批量无感连接'功能"
    
elif [ "$SYSTEM_APP" = true ]; then
    echo "⚠️  部分功能可用"
    echo ""
    echo "问题："
    echo "  - 特权权限未正确获得"
    echo ""
    echo "解决方案："
    echo "  1. 重启设备确保权限生效"
    echo "  2. 检查权限配置文件是否正确"
    echo "  3. 重新运行安装脚本"
    
else
    echo "❌ 无感连接功能不可用"
    echo ""
    echo "问题："
    echo "  - 应用未安装为系统应用"
    echo "  - 缺少特权权限"
    echo ""
    echo "解决方案："
    echo "  1. 运行 install_as_system_app.sh 脚本"
    echo "  2. 确保设备已root"
    echo "  3. 重启设备"
fi

echo ""
echo "=========================================="

# 启动应用进行实际测试
read -p "是否启动应用进行实际测试？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在启动应用..."
    am start -n "$PACKAGE_NAME/.MainActivity"
    echo "应用已启动，请在应用中测试无感连接功能"
fi

echo "测试完成！"
