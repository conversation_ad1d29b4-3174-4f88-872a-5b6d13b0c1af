#!/bin/bash

# 树莓派蓝牙控制应用 - 系统应用安装脚本
# 此脚本将应用安装为系统应用以获得BLUETOOTH_PRIVILEGED权限

echo "=========================================="
echo "树莓派蓝牙控制应用 - 系统应用安装脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "错误: 此脚本需要root权限运行"
    echo "请使用: sudo $0"
    exit 1
fi

# 应用包名和APK路径
PACKAGE_NAME="com.example.exhibition_car_control"
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
SYSTEM_APP_DIR="/system/app/ExhibitionCarControl"
SYSTEM_PRIV_APP_DIR="/system/priv-app/ExhibitionCarControl"

echo "正在检查APK文件..."
if [ ! -f "$APK_PATH" ]; then
    echo "错误: 找不到APK文件: $APK_PATH"
    echo "请先编译应用: ./gradlew assembleDebug"
    exit 1
fi

echo "APK文件找到: $APK_PATH"

# 检查设备是否已root
echo "正在检查root权限..."
if ! command -v su &> /dev/null; then
    echo "警告: 未检测到su命令，请确保设备已root"
fi

# 卸载现有的用户应用版本
echo "正在卸载现有的用户应用版本..."
pm uninstall $PACKAGE_NAME 2>/dev/null || echo "未找到现有用户应用"

# 重新挂载系统分区为可写
echo "正在重新挂载系统分区..."
mount -o remount,rw /system

# 创建系统应用目录（使用priv-app获得更高权限）
echo "正在创建系统应用目录..."
mkdir -p "$SYSTEM_PRIV_APP_DIR"

# 复制APK到系统目录
echo "正在复制APK到系统目录..."
cp "$APK_PATH" "$SYSTEM_PRIV_APP_DIR/ExhibitionCarControl.apk"

# 设置正确的权限
echo "正在设置文件权限..."
chmod 644 "$SYSTEM_PRIV_APP_DIR/ExhibitionCarControl.apk"
chown root:root "$SYSTEM_PRIV_APP_DIR/ExhibitionCarControl.apk"

# 创建权限配置文件
echo "正在创建权限配置文件..."
cat > "/system/etc/permissions/com.example.exhibition_car_control.xml" << EOF
<?xml version="1.0" encoding="utf-8"?>
<permissions>
    <privapp-permissions package="$PACKAGE_NAME">
        <permission name="android.permission.BLUETOOTH_PRIVILEGED"/>
        <permission name="android.permission.BLUETOOTH_STACK"/>
        <permission name="android.permission.BLUETOOTH_MAP"/>
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
    </privapp-permissions>
</permissions>
EOF

chmod 644 "/system/etc/permissions/com.example.exhibition_car_control.xml"
chown root:root "/system/etc/permissions/com.example.exhibition_car_control.xml"

# 重新挂载系统分区为只读
echo "正在重新挂载系统分区为只读..."
mount -o remount,ro /system

# 重启包管理器服务
echo "正在重启包管理器服务..."
killall system_server 2>/dev/null || echo "无法重启system_server，可能需要重启设备"

# 等待系统稳定
echo "等待系统稳定..."
sleep 5

# 验证安装
echo "正在验证安装..."
if pm list packages | grep -q "$PACKAGE_NAME"; then
    echo "✅ 应用已成功安装为系统应用"
    
    # 检查权限
    echo "正在检查权限..."
    if dumpsys package $PACKAGE_NAME | grep -q "android.permission.BLUETOOTH_PRIVILEGED"; then
        echo "✅ BLUETOOTH_PRIVILEGED权限已获得"
    else
        echo "⚠️  BLUETOOTH_PRIVILEGED权限可能未正确获得"
    fi
else
    echo "❌ 应用安装失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "安装完成！"
echo "=========================================="
echo ""
echo "重要提示："
echo "1. 应用已安装为系统特权应用"
echo "2. 已获得BLUETOOTH_PRIVILEGED权限"
echo "3. 现在支持无感蓝牙连接功能"
echo "4. 建议重启设备以确保所有权限生效"
echo ""
echo "如需卸载系统应用，请运行："
echo "sudo rm -rf $SYSTEM_PRIV_APP_DIR"
echo "sudo rm -f /system/etc/permissions/com.example.exhibition_car_control.xml"
echo ""

# 询问是否重启
read -p "是否现在重启设备以确保权限生效？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在重启设备..."
    reboot
else
    echo "请手动重启设备以确保所有权限正确生效"
fi
