# 蓝牙广播功能测试指南

## 测试环境准备

### 树莓派端
1. 确保应用已安装为系统应用（具有特权权限）
2. 确保蓝牙功能正常工作
3. 记录原始系统蓝牙名称

### 手机端
1. 确保蓝牙功能开启
2. 清除之前配对的相关设备
3. 准备进行蓝牙设备搜索

## 测试步骤

### 第一步：启动应用蓝牙广播

#### 自动启动测试
1. 在树莓派上启动应用
2. 观察应用是否自动启动蓝牙广播
3. 检查UI中的广播状态显示
4. 预期结果：
   - 广播状态显示为"广播中：ExhibitionCar-Pi"
   - 蓝牙名称从原始名称变更为"ExhibitionCar-Pi"

#### 手动启动测试
1. 如果自动启动失败，使用UI中的"启动广播"按钮
2. 观察广播状态变化
3. 检查设备名称是否更新

### 第二步：手机端发现测试

#### 蓝牙设备搜索
1. 在手机上打开蓝牙设置
2. 搜索可用设备
3. 查找"ExhibitionCar-Pi"设备
4. 预期结果：
   - 能够发现"ExhibitionCar-Pi"设备
   - 设备显示为可连接状态

#### 连接测试
1. 点击连接"ExhibitionCar-Pi"设备
2. 观察连接过程
3. 检查应用端是否收到连接请求
4. 预期结果：
   - 连接成功
   - 应用端显示新的连接
   - 消息能够正常收发

### 第三步：功能验证测试

#### 消息传输测试
1. 从手机端发送测试消息
2. 检查树莓派应用是否收到消息
3. 从树莓派发送回复消息
4. 检查手机端是否收到回复
5. 预期结果：
   - 双向消息传输正常
   - 消息内容完整无误

#### 广播状态监控
1. 观察UI中的广播状态指示器
2. 检查设备名称显示是否正确
3. 验证"手机可搜索到此设备名称进行连接"提示
4. 预期结果：
   - 状态指示器为蓝色（广播中）
   - 设备名称显示为"ExhibitionCar-Pi"
   - 提示信息正确显示

### 第四步：停止广播测试

#### 手动停止测试
1. 点击UI中的"停止广播"按钮
2. 观察广播状态变化
3. 检查设备名称是否恢复原始名称
4. 预期结果：
   - 广播状态变为"广播已停止"
   - 设备名称恢复为原始系统名称
   - 状态指示器变为灰色

#### 手机端验证
1. 在手机上重新搜索蓝牙设备
2. 检查是否还能发现"ExhibitionCar-Pi"
3. 检查是否能发现原始系统蓝牙名称
4. 预期结果：
   - 无法发现"ExhibitionCar-Pi"设备
   - 能够发现原始系统蓝牙名称

### 第五步：重启和恢复测试

#### 应用重启测试
1. 停止应用
2. 重新启动应用
3. 检查是否自动恢复蓝牙广播
4. 验证设备名称和广播状态

#### 系统重启测试
1. 重启树莓派系统
2. 启动应用
3. 检查蓝牙广播功能是否正常
4. 验证原始设备名称是否正确保存和恢复

## 测试检查清单

### 基础功能
- [ ] 应用启动时自动开启蓝牙广播
- [ ] 设备名称正确更改为"ExhibitionCar-Pi"
- [ ] 手机能够发现应用蓝牙设备
- [ ] 手机能够成功连接到应用
- [ ] 消息能够正常双向传输

### UI显示
- [ ] 广播状态指示器正确显示（蓝色=广播中，灰色=已停止）
- [ ] 当前蓝牙名称正确显示
- [ ] 广播控制按钮功能正常（启动/停止）
- [ ] 用户提示信息准确显示

### 权限和兼容性
- [ ] 标准权限下能够请求可发现性
- [ ] 系统级权限下能够直接设置可发现性
- [ ] 权限不足时有适当的错误提示
- [ ] 在不同Android版本上功能正常

### 资源管理
- [ ] 停止广播时正确恢复原始设备名称
- [ ] 应用退出时自动清理定时器
- [ ] 内存使用正常，无泄漏
- [ ] 定时刷新机制工作正常

## 常见问题及解决方案

### 问题1：手机无法发现应用设备
**可能原因**：
- 蓝牙广播未启动
- 可发现性未启用
- 权限不足

**解决方案**：
- 检查广播状态指示器
- 手动点击"启动广播"按钮
- 确认蓝牙权限已授予

### 问题2：连接到系统蓝牙而不是应用
**可能原因**：
- 应用广播未正常工作
- 手机缓存了旧的配对信息

**解决方案**：
- 确认应用广播状态为"广播中"
- 清除手机蓝牙缓存
- 重新搜索设备

### 问题3：设备名称未更改
**可能原因**：
- 蓝牙权限不足
- 系统限制

**解决方案**：
- 检查应用是否为系统应用
- 确认BLUETOOTH_ADMIN权限
- 查看日志中的错误信息

### 问题4：广播状态显示异常
**可能原因**：
- UI状态同步问题
- 蓝牙适配器状态异常

**解决方案**：
- 重启应用
- 检查蓝牙适配器状态
- 查看相关日志信息

## 测试报告模板

### 测试环境
- 树莓派型号：
- Android版本：
- 应用版本：
- 测试日期：

### 测试结果
- 基础功能：✅/❌
- UI显示：✅/❌
- 权限兼容性：✅/❌
- 资源管理：✅/❌

### 发现问题
1. 问题描述：
   - 复现步骤：
   - 预期结果：
   - 实际结果：

### 建议改进
1. 功能改进建议：
2. UI优化建议：
3. 性能优化建议：
