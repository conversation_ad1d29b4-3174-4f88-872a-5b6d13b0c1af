# 蓝牙广播功能实现文档

## 概述

为了解决手机连接到树莓派系统层面蓝牙而不是应用层蓝牙的问题，我们实现了应用层蓝牙广播功能。现在手机可以发现并连接到应用专用的蓝牙名称，确保连接走我们开发的应用逻辑。

## 核心功能

### 1. 应用层蓝牙广播
- **功能**：设置应用专用的蓝牙设备名称
- **目的**：区别于系统层蓝牙，让手机连接到应用而不是系统
- **实现**：通过 `BluetoothAdapter.setName()` 设置自定义名称

### 2. 自动可发现性管理
- **功能**：自动启用蓝牙可发现性
- **持续时间**：5分钟（300秒）
- **刷新机制**：每30秒自动刷新可发现性状态

### 3. 智能权限处理
- **标准模式**：通过Intent请求用户启用可发现性
- **特权模式**：如果有系统级权限，直接设置可发现性

## 配置参数

### DeviceConfig.BluetoothBroadcast
```kotlin
object BluetoothBroadcast {
    // 应用层蓝牙设备名称（区别于系统层蓝牙）
    val appBluetoothName: String = if (isRaspberryPiVersion) "ExhibitionCar-Pi" else "ExhibitionCar-Phone"
    
    // 蓝牙服务名称
    val serviceName: String = "ExhibitionCarControl"
    
    // 是否启用应用层蓝牙广播
    val enableAppBroadcast: Boolean = isRaspberryPiVersion
    
    // 蓝牙可发现性持续时间（秒）
    val discoverableDuration: Int = 300  // 5分钟
    
    // 广播间隔（毫秒）
    val broadcastInterval: Long = 30000L  // 30秒
}
```

## 核心API

### BluetoothManager 新增方法

#### 1. 启动应用蓝牙广播
```kotlin
fun startAppBluetoothBroadcast(appName: String): Boolean
```
- **参数**：`appName` - 应用专用蓝牙名称
- **返回**：成功返回true，失败返回false
- **功能**：
  - 保存原始蓝牙名称
  - 设置应用专用名称
  - 启用可发现性
  - 启动定时刷新

#### 2. 停止应用蓝牙广播
```kotlin
fun stopAppBluetoothBroadcast(): Boolean
```
- **功能**：
  - 停止定时刷新
  - 恢复原始蓝牙名称
  - 清理广播状态

#### 3. 检查广播状态
```kotlin
fun isBroadcasting(): Boolean
fun getCurrentBluetoothName(): String?
```

### 统一服务器启动增强
```kotlin
fun startUnifiedBluetoothServer(appBluetoothName: String? = null): Boolean
```
- **新增参数**：`appBluetoothName` - 可选的应用蓝牙名称
- **功能**：启动服务器时自动开启蓝牙广播

## UI 集成

### 1. 服务器状态显示
- **位置**：BluetoothServerSection
- **功能**：显示蓝牙广播状态和当前设备名称
- **仅树莓派版本显示**

### 2. 广播控制面板
- **位置**：BluetoothControlSection - SilentConnectionCard
- **功能**：
  - 显示广播状态
  - 启动/停止广播按钮
  - 实时显示当前蓝牙名称
  - 用户提示信息

### 3. 自动启动集成
- **位置**：BluetoothDemoScreen
- **功能**：树莓派版本自动启动时包含蓝牙广播

## 使用流程

### 树莓派端（自动）
1. 应用启动时自动开启蓝牙广播
2. 设备名称变更为 "ExhibitionCar-Pi"
3. 启用可发现性，持续5分钟
4. 每30秒自动刷新可发现性

### 手机端连接
1. 手机搜索蓝牙设备
2. 发现 "ExhibitionCar-Pi" 设备
3. 连接该设备
4. 连接请求直接到达应用层，而不是系统层

## 技术实现细节

### 1. 名称管理
- 启动广播时保存原始系统蓝牙名称
- 设置应用专用名称
- 停止广播时恢复原始名称

### 2. 可发现性管理
- 优先使用Intent请求用户授权
- 如果有系统级权限，使用反射直接设置
- 定时刷新确保持续可发现

### 3. 定时器管理
- 使用Timer定期刷新广播状态
- 应用关闭或停止广播时自动清理定时器

## 权限要求

### 基础权限
- `BLUETOOTH`
- `BLUETOOTH_ADMIN`
- `ACCESS_FINE_LOCATION`

### 系统级权限（可选，用于无感操作）
- `BLUETOOTH_PRIVILEGED`
- `BLUETOOTH_STACK`

## 测试验证

### 1. 功能测试
- [ ] 启动广播后手机能发现应用设备名称
- [ ] 连接成功后消息能正确到达应用
- [ ] 停止广播后恢复原始设备名称
- [ ] 定时刷新机制正常工作

### 2. 权限测试
- [ ] 标准权限下通过Intent请求可发现性
- [ ] 系统级权限下直接设置可发现性
- [ ] 权限不足时的错误处理

### 3. UI测试
- [ ] 广播状态正确显示
- [ ] 控制按钮功能正常
- [ ] 设备名称实时更新

## 注意事项

1. **设备名称冲突**：确保应用蓝牙名称与系统名称不同
2. **权限处理**：妥善处理可发现性权限请求
3. **资源清理**：应用退出时确保停止广播和清理定时器
4. **兼容性**：在不同Android版本上测试功能
5. **用户体验**：提供清晰的状态提示和操作指导

## 故障排除

### 常见问题
1. **设备名称未更改**：检查蓝牙权限
2. **手机无法发现**：确认可发现性已启用
3. **连接到系统蓝牙**：确认应用广播正在运行
4. **定时器未工作**：检查Timer初始化和清理

### 调试方法
- 查看日志中的广播状态信息
- 使用UI面板监控实时状态
- 检查蓝牙适配器状态
- 验证权限获取情况
