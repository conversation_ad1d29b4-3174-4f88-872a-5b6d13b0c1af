package com.zerosense.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothServerSocket
import android.bluetooth.BluetoothSocket
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.content.ContextCompat
import java.io.IOException
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 蓝牙管理器 - SDK 核心类
 */
class BluetoothManager private constructor(private val context: Context) {

    // 先定义handler，确保在init块中可以使用
    private val handler = Handler(Looper.getMainLooper())

    init {
        Log.d(TAG, "BluetoothManager 开始初始化...")
        try {
            // 延迟启动连接池清理定时器，避免初始化时的潜在问题
            handler.postDelayed({
                startConnectionCleanupTimer()
            }, 1000) // 1秒后启动

            Log.d(TAG, "BluetoothManager 初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "BluetoothManager 初始化失败: ${e.message}", e)
            throw e
        }
    }

    companion object {
        private const val TAG = "BluetoothManager"

        @Volatile
        private var INSTANCE: BluetoothManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): BluetoothManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BluetoothManager(context.applicationContext).also { INSTANCE = it }
            }
        }

        // 标准 SPP UUID
        private val SPP_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
        
        // 扫描超时时间（毫秒）
        private const val SCAN_TIMEOUT = 12000L

        // 连接超时时间（毫秒）- 给用户足够时间确认配对
        private const val CONNECT_TIMEOUT = 30000L

        // 已配对设备连接超时时间（毫秒）- 已配对设备连接更快
        private const val PAIRED_DEVICE_CONNECT_TIMEOUT = 20000L
    }

    private var bluetoothAdapter: BluetoothAdapter? = null
    private val callbacks = mutableSetOf<BluetoothCallback>()
    private val discoveredDevices = ConcurrentHashMap<String, BluetoothDeviceInfo>()
    private val connectedDevices = ConcurrentHashMap<String, BluetoothSocket>()
    private val connectingDevices = ConcurrentHashMap<String, Boolean>() // 正在连接的设备

    // 持久连接池 - 用于高效数据传输
    private val connectionPool = ConcurrentHashMap<String, BluetoothSocket>()
    private val connectionLastUsed = ConcurrentHashMap<String, Long>()
    private val CONNECTION_TIMEOUT = 30000L // 30秒无活动后关闭连接
    
    private var currentScanState = BluetoothScanState.IDLE
    private var scanTimeoutRunnable: Runnable? = null
    private var isReceiverRegistered = false

    // 连接池清理定时器
    private var connectionCleanupRunnable: Runnable? = null

    // 蓝牙服务器相关
    private var bluetoothServerSocket: BluetoothServerSocket? = null
    private var serverThread: Thread? = null
    private var isServerRunning = false

    // BLE管理器 - 延迟初始化
    private var bleManager: BleManager? = null

    // 蓝牙广播相关
    private var originalBluetoothName: String? = null
    private var isBroadcasting = false
    private var broadcastTimer: Timer? = null

    // 主动配对相关
    private var enableProactivePairing = false
    private val pendingPairings = mutableSetOf<String>()
    
    // 蓝牙广播接收器
    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                BluetoothDevice.ACTION_FOUND -> {
                    val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                    }
                    val rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE).toInt()
                    
                    device?.let {
                        val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(it, rssi)
                        discoveredDevices[it.address] = deviceInfo
                        notifyDeviceFound(deviceInfo)
                    }
                }
                
                BluetoothAdapter.ACTION_DISCOVERY_STARTED -> {
                    updateScanState(BluetoothScanState.SCANNING)
                }
                
                BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                    updateScanState(BluetoothScanState.SCAN_FINISHED)
                    stopScanTimeout()
                }

                BluetoothDevice.ACTION_BOND_STATE_CHANGED -> {
                    val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                    }
                    val bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.BOND_NONE)
                    val previousBondState = intent.getIntExtra(BluetoothDevice.EXTRA_PREVIOUS_BOND_STATE, BluetoothDevice.BOND_NONE)

                    device?.let {
                        when (bondState) {
                            BluetoothDevice.BOND_BONDED -> {
                                if (previousBondState == BluetoothDevice.BOND_BONDING) {
                                    // 配对刚刚成功，检查是否有正在连接的请求
                                    if (connectingDevices.containsKey(it.address)) {
                                        handler.post {
                                            notifyError("配对成功！正在建立连接...")
                                        }
                                        // 使用专门的配对后连接方法
                                        connectAfterPairing(it.address)
                                    }
                                }
                                pendingPairings.remove(it.address)
                            }
                            BluetoothDevice.BOND_BONDING -> {
                                // 设备配对中
                                Log.d(TAG, "设备配对中: ${it.name} (${it.address})")

                                // 如果启用了主动配对，自动确认配对
                                if (enableProactivePairing && supportsSeamlessConnection()) {
                                    handler.postDelayed({
                                        tryAutoConfirmPairing(it)
                                    }, 500) // 延迟500ms后自动确认
                                }
                            }
                            BluetoothDevice.BOND_NONE -> {
                                if (previousBondState == BluetoothDevice.BOND_BONDING) {
                                    // 配对失败
                                    connectingDevices.remove(it.address)
                                    pendingPairings.remove(it.address)
                                    handler.post {
                                        notifyError("配对失败，请重试")
                                    }
                                }
                            }
                            else -> {
                                // 其他状态
                                Log.d(TAG, "未知配对状态: $bondState")
                            }
                        }
                    }
                }

                // 监听配对请求
                BluetoothDevice.ACTION_PAIRING_REQUEST -> {
                    val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                    }

                    device?.let { dev ->
                        Log.d(TAG, "收到配对请求: ${dev.name} (${dev.address})")

                        if (enableProactivePairing && supportsSeamlessConnection()) {
                            // 自动处理配对请求
                            handleIncomingPairingRequest(dev, intent)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 添加回调监听器
     */
    fun addCallback(callback: BluetoothCallback) {
        callbacks.add(callback)
    }
    
    /**
     * 移除回调监听器
     */
    fun removeCallback(callback: BluetoothCallback) {
        callbacks.remove(callback)
    }
    
    /**
     * 初始化蓝牙适配器（如果尚未初始化）
     */
    private fun initializeBluetoothAdapter() {
        if (bluetoothAdapter == null) {
            try {
                // 直接获取蓝牙适配器，不使用阻塞等待
                bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                if (bluetoothAdapter != null) {
                    Log.d(TAG, "蓝牙适配器初始化成功")
                } else {
                    Log.w(TAG, "蓝牙适配器获取为null - 设备可能不支持蓝牙")
                }
            } catch (e: Exception) {
                Log.e(TAG, "蓝牙适配器初始化异常: ${e.message}", e)
                bluetoothAdapter = null
            }
        }
    }

    /**
     * 检查蓝牙是否可用
     */
    fun isBluetoothAvailable(): Boolean {
        initializeBluetoothAdapter()
        val available = bluetoothAdapter != null
        Log.d(TAG, "蓝牙可用性检查: $available")
        return available
    }
    
    /**
     * 检查蓝牙是否已启用
     */
    fun isBluetoothEnabled(): Boolean {
        initializeBluetoothAdapter()
        val enabled = bluetoothAdapter?.isEnabled == true
        Log.d(TAG, "蓝牙启用状态检查: $enabled")
        return enabled
    }
    
    /**
     * 检查是否有必要的权限
     */
    fun hasRequiredPermissions(): Boolean {
        val permissions = getRequiredPermissions()
        return permissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取需要的权限列表
     */
    fun getRequiredPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        } else {
            arrayOf(
                Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        }
    }

    /**
     * 检查是否有系统级蓝牙权限（用于无感连接）
     */
    fun hasPrivilegedBluetoothPermissions(): Boolean {
        val privilegedPermissions = arrayOf(
            "android.permission.BLUETOOTH_PRIVILEGED",
            "android.permission.BLUETOOTH_STACK"
        )
        return privilegedPermissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查是否支持无感连接
     */
    fun supportsSeamlessConnection(): Boolean {
        return hasPrivilegedBluetoothPermissions()
    }
    
    /**
     * 开始扫描蓝牙设备
     */
    fun startScan(): Boolean {
        if (!isBluetoothAvailable()) {
            notifyError("蓝牙不可用")
            return false
        }
        
        if (!isBluetoothEnabled()) {
            notifyError("蓝牙未启用")
            return false
        }
        
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }
        
        if (currentScanState == BluetoothScanState.SCANNING) {
            return true // 已在扫描中
        }
        
        // 清空之前的发现设备
        discoveredDevices.clear()
        
        // 注册广播接收器
        registerReceiver()
        
        // 开始扫描
        val started = bluetoothAdapter?.startDiscovery() == true
        if (started) {
            startScanTimeout()
        } else {
            notifyError("启动扫描失败")
            unregisterReceiver()
        }
        
        return started
    }
    
    /**
     * 停止扫描蓝牙设备
     */
    fun stopScan() {
        bluetoothAdapter?.cancelDiscovery()
        stopScanTimeout()
        unregisterReceiver()
        updateScanState(BluetoothScanState.SCAN_FINISHED)
    }
    
    /**
     * 获取已发现的设备列表
     */
    fun getDiscoveredDevices(): List<BluetoothDeviceInfo> {
        return discoveredDevices.values.toList()
    }
    
    /**
     * 获取已配对的设备列表
     */
    fun getPairedDevices(): List<BluetoothDeviceInfo> {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return emptyList()
        }

        return bluetoothAdapter?.bondedDevices?.map { device ->
            BluetoothDeviceInfo.fromBluetoothDevice(device).copy(isPaired = true)
        } ?: emptyList()
    }

    /**
     * 发送数据到指定设备（高效版本 - 使用连接池）
     */
    fun sendData(deviceAddress: String, data: String): Boolean {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        if (device == null) {
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        // 检查设备是否已配对
        if (device.bondState != BluetoothDevice.BOND_BONDED) {
            notifyError("设备未配对，请先配对设备")
            return false
        }

        // 在后台线程中发送数据（使用连接池）
        Thread {
            sendDataFast(device, data)
        }.start()

        return true
    }

    /**
     * 高效数据发送方法（使用连接池）
     */
    private fun sendDataFast(device: BluetoothDevice, data: String) {
        val deviceAddress = device.address
        val startTime = System.currentTimeMillis()

        try {
            // 获取或创建连接
            val socket = getOrCreateConnection(device)
            if (socket == null) {
                handler.post {
                    notifyError("无法建立连接到设备: ${device.name ?: deviceAddress}")
                }
                return
            }

            // 发送数据
            val outputStream = socket.outputStream
            outputStream.write(data.toByteArray(Charsets.UTF_8))
            outputStream.flush()

            // 更新最后使用时间
            connectionLastUsed[deviceAddress] = System.currentTimeMillis()

            val sendTime = System.currentTimeMillis() - startTime
            handler.post {
                notifySuccess("数据发送成功 (${sendTime}ms): \"$data\"")
            }

            // 可选：读取响应（使用非阻塞方式避免卡住）
            try {
                val inputStream = socket.inputStream
                if (inputStream.available() > 0) {
                    val buffer = ByteArray(1024)
                    val bytesRead = inputStream.read(buffer)
                    if (bytesRead > 0) {
                        val response = String(buffer, 0, bytesRead, Charsets.UTF_8)
                        handler.post {
                            notifyInfo("收到响应: \"$response\"")
                        }
                    }
                }
            } catch (e: Exception) {
                // 没有响应数据，这是正常的
            }

        } catch (e: Exception) {
            handler.post {
                notifyError("数据发送失败: ${e.message}")
            }
            // 连接可能已断开，从连接池中移除
            removeFromConnectionPool(deviceAddress)
        }
    }

    /**
     * 获取或创建到设备的连接
     */
    private fun getOrCreateConnection(device: BluetoothDevice): BluetoothSocket? {
        val deviceAddress = device.address

        // 检查连接池中是否有可用连接
        val existingSocket = connectionPool[deviceAddress]
        if (existingSocket != null && existingSocket.isConnected) {
            // 连接仍然有效，直接使用
            return existingSocket
        }

        // 需要创建新连接
        return createNewConnection(device)
    }

    /**
     * 创建新的连接并加入连接池（使用可靠的多重连接方法）
     */
    private fun createNewConnection(device: BluetoothDevice): BluetoothSocket? {
        val deviceAddress = device.address

        try {
            handler.post {
                notifyInfo("建立新连接到: ${device.name ?: deviceAddress}")
            }

            // 停止扫描以提高连接成功率
            bluetoothAdapter?.cancelDiscovery()

            // 使用可靠的多重连接方法
            val socket = createBluetoothSocket(device, 0, false)
            if (socket == null) {
                handler.post {
                    notifyError("无法创建蓝牙Socket")
                }
                return null
            }

            // 建立连接（设置较短的超时时间用于快速发送）
            socket.connect()

            // 加入连接池
            connectionPool[deviceAddress] = socket
            connectionLastUsed[deviceAddress] = System.currentTimeMillis()

            handler.post {
                notifySuccess("连接建立成功: ${device.name ?: deviceAddress}")
            }

            return socket

        } catch (e: Exception) {
            handler.post {
                notifyError("建立连接失败: ${e.message}")
            }
            return null
        }
    }

    /**
     * 从连接池中移除连接
     */
    private fun removeFromConnectionPool(deviceAddress: String) {
        val socket = connectionPool.remove(deviceAddress)
        connectionLastUsed.remove(deviceAddress)

        try {
            socket?.close()
        } catch (e: Exception) {
            // 忽略关闭异常
        }
    }

    /**
     * 清理过期的连接
     */
    private fun cleanupExpiredConnections() {
        val currentTime = System.currentTimeMillis()
        val expiredDevices = mutableListOf<String>()

        connectionLastUsed.forEach { (deviceAddress, lastUsed) ->
            if (currentTime - lastUsed > CONNECTION_TIMEOUT) {
                expiredDevices.add(deviceAddress)
            }
        }

        expiredDevices.forEach { deviceAddress ->
            removeFromConnectionPool(deviceAddress)
        }
    }

    /**
     * 启动连接池清理定时器
     */
    private fun startConnectionCleanupTimer() {
        connectionCleanupRunnable = object : Runnable {
            override fun run() {
                cleanupExpiredConnections()
                // 每10秒检查一次过期连接
                handler.postDelayed(this, 10000)
            }
        }
        handler.postDelayed(connectionCleanupRunnable!!, 10000)
    }

    /**
     * 停止连接池清理定时器
     */
    private fun stopConnectionCleanupTimer() {
        connectionCleanupRunnable?.let { runnable ->
            handler.removeCallbacks(runnable)
            connectionCleanupRunnable = null
        }
    }

    /**
     * 关闭所有连接池连接（用于应用退出时清理）
     */
    fun closeAllConnections() {
        stopConnectionCleanupTimer()

        connectionPool.values.forEach { socket ->
            try {
                socket.close()
            } catch (e: Exception) {
                // 忽略关闭异常
            }
        }

        connectionPool.clear()
        connectionLastUsed.clear()
    }

    /**
     * 配对成功后的处理（现在只是更新状态）
     */
    fun connectAfterPairing(deviceAddress: String): Boolean {
        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress) ?: return false
        val deviceInfo = BluetoothDeviceInfo(
            name = device.name ?: "未知设备",
            address = device.address,
            isConnected = true, // 配对完成即视为可用
            isPaired = true
        )

        // 清除配对状态
        connectingDevices.remove(deviceAddress)

        // 更新状态为已连接（实际是已配对可用）
        handler.post {
            notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTED)
            notifyError("配对成功！现在可以发送数据了")
        }

        return true
    }

    /**
     * 连接到指定设备（实际上是触发配对过程）
     */
    fun connectToDevice(deviceAddress: String): Boolean {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        if (device == null) {
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        // 检查设备是否已配对
        if (device.bondState == BluetoothDevice.BOND_BONDED) {
            notifyError("设备已配对，可以直接发送数据")
            val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device)
            notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTED)
            return true
        }

        // 如果正在配对，不重复操作
        if (connectingDevices.containsKey(deviceAddress)) {
            return true
        }

        val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device)
        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTING)

        // 标记为正在配对
        connectingDevices[deviceAddress] = true

        // 检查是否支持无感连接
        if (supportsSeamlessConnection()) {
            notifyError("检测到系统级权限，尝试无感连接...")
            return attemptSeamlessConnection(device, deviceInfo)
        } else {
            // 开始传统配对过程
            notifyError("开始配对设备，请在目标设备上确认配对请求...")
            val paired = device.createBond()

            if (!paired) {
                connectingDevices.remove(deviceAddress)
                notifyError("无法启动配对过程")
                notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.DISCONNECTED)
                return false
            }

            return true
        }
    }

    /**
     * 内部连接方法，支持重试和多种连接方式
     */
    private fun connectToDeviceInternal(device: BluetoothDevice, deviceInfo: BluetoothDeviceInfo, retryCount: Int) {
        val maxRetries = 1 // 减少重试次数，给用户更多时间
        val isPaired = device.bondState == BluetoothDevice.BOND_BONDED
        // 检查设备当前的配对状态（可能在连接过程中发生了变化）
        val currentlyPaired = device.bondState == BluetoothDevice.BOND_BONDED

        try {
            // 停止扫描以提高连接成功率
            bluetoothAdapter?.cancelDiscovery()

            // 等待一下让扫描完全停止
            Thread.sleep(800)

            // 检测设备支持的服务
            detectDeviceServices(device)

            if (!isPaired && currentlyPaired) {
                // 设备刚刚配对成功，等待更长时间让配对过程完全完成
                handler.post {
                    notifyError("配对成功！正在建立连接...")
                }
                Thread.sleep(2000) // 等待2秒让配对过程稳定
            }

            // 尝试多种连接方法
            val socket = createBluetoothSocket(device, retryCount, !isPaired && currentlyPaired)

            if (socket == null) {
                handler.post {
                    notifyError("无法创建蓝牙Socket")
                }
                connectingDevices.remove(device.address)
                return
            }

            // 根据设备配对状态设置不同的超时时间（使用当前状态）
            val timeout = if (currentlyPaired) PAIRED_DEVICE_CONNECT_TIMEOUT else CONNECT_TIMEOUT

            handler.post {
                notifyError("开始连接，超时时间: ${timeout/1000}秒")
            }

            // 设置连接超时
            val timeoutRunnable = Runnable {
                try {
                    socket.close()
                } catch (e: IOException) {
                    // 忽略关闭异常
                }
                handler.post {
                    if (retryCount < maxRetries) {
                        // 提示用户正在重试
                        notifyError("连接超时，正在重试...（${retryCount + 1}/${maxRetries + 1}）")
                        // 重试连接
                        Thread {
                            Thread.sleep(3000) // 等待3秒后重试，给用户更多时间
                            connectToDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    } else {
                        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_TIMEOUT)
                        if (isPaired) {
                            notifyError("连接超时。设备可能不在范围内或已被其他设备连接")
                        } else {
                            notifyError("连接超时。请确保在设备上确认配对请求，或检查设备是否支持连接")
                        }
                    }
                }
            }
            handler.postDelayed(timeoutRunnable, timeout)

            // 提供用户反馈
            if (retryCount == 0 && !currentlyPaired) {
                handler.post {
                    notifyError("正在连接设备，请在目标设备上确认配对请求...")
                }
            } else if (retryCount == 0 && currentlyPaired) {
                handler.post {
                    notifyError("正在连接已配对设备...")
                }
            }

            // 执行连接
            handler.post {
                notifyError("正在建立Socket连接...")
            }

            val startTime = System.currentTimeMillis()
            socket.connect()
            val connectTime = System.currentTimeMillis() - startTime

            // 连接成功，取消超时
            handler.removeCallbacks(timeoutRunnable)

            handler.post {
                notifyError("Socket连接成功，耗时: ${connectTime}ms")
            }

            // 保存连接
            connectedDevices[device.address] = socket
            // 清除连接状态
            connectingDevices.remove(device.address)

            handler.post {
                notifyConnectionStateChanged(
                    deviceInfo.copy(isConnected = true),
                    BluetoothConnectionState.CONNECTED
                )
                if (retryCount > 0) {
                    notifyError("连接成功！（第 ${retryCount + 1} 次尝试）")
                } else {
                    notifyError("连接成功！")
                }
            }

        } catch (e: IOException) {
            handler.post {
                if (retryCount < maxRetries) {
                    // 提示用户正在重试
                    notifyError("连接失败，正在重试...（${retryCount + 1}/${maxRetries + 1}）")

                    // 在重试前尝试重置蓝牙连接状态
                    if (retryCount == 0 && currentlyPaired) {
                        notifyError("尝试重置设备连接状态...")
                        Thread {
                            try {
                                // 尝试清除设备缓存（需要反射调用）
                                val refreshMethod = device.javaClass.getMethod("refresh")
                                refreshMethod.invoke(device)
                                Thread.sleep(2000)
                                handler.post {
                                    notifyError("设备状态重置完成，开始重试连接...")
                                }
                            } catch (ex: Exception) {
                                handler.post {
                                    notifyError("设备状态重置失败，直接重试连接...")
                                }
                            }
                            Thread.sleep(3000) // 等待3秒后重试
                            connectToDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    } else {
                        // 重试连接
                        Thread {
                            Thread.sleep(3000) // 等待3秒后重试
                            connectToDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    }
                } else {
                    // 清除连接状态
                    connectingDevices.remove(device.address)
                    notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_FAILED)

                    // 提供更详细的错误信息
                    val errorMsg = when {
                        e.message?.contains("read failed") == true -> {
                            if (currentlyPaired) {
                                "连接失败：设备可能不在范围内、电量不足或正被其他设备使用。请确保设备处于可连接状态。"
                            } else {
                                "连接失败：配对可能未完成或设备不支持此连接方式。请重新尝试配对。"
                            }
                        }
                        e.message?.contains("timeout") == true -> {
                            "连接超时：设备响应时间过长，请检查设备状态并重试。"
                        }
                        e.message?.contains("Service discovery failed") == true -> {
                            "服务发现失败：设备可能不支持所需的蓝牙服务。"
                        }
                        else -> "连接失败: ${e.message}"
                    }
                    notifyError(errorMsg)
                }
            }
        }
    }

    /**
     * 取消正在进行的连接
     */
    fun cancelConnection(deviceAddress: String): Boolean {
        if (!connectingDevices.containsKey(deviceAddress)) {
            return false // 没有正在连接的设备
        }

        connectingDevices.remove(deviceAddress)

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        val deviceInfo = device?.let { BluetoothDeviceInfo.fromBluetoothDevice(it) }

        deviceInfo?.let {
            notifyConnectionStateChanged(it, BluetoothConnectionState.DISCONNECTED)
            notifyError("连接已取消")
        }

        return true
    }

    /**
     * 断开与指定设备的连接
     */
    fun disconnectFromDevice(deviceAddress: String): Boolean {
        val socket = connectedDevices[deviceAddress]
        if (socket == null) {
            return false // 设备未连接
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        val deviceInfo = device?.let { BluetoothDeviceInfo.fromBluetoothDevice(it) }

        deviceInfo?.let {
            notifyConnectionStateChanged(it, BluetoothConnectionState.DISCONNECTING)
        }

        Thread {
            try {
                socket.close()
                connectedDevices.remove(deviceAddress)

                handler.post {
                    deviceInfo?.let {
                        notifyConnectionStateChanged(
                            it.copy(isConnected = false),
                            BluetoothConnectionState.DISCONNECTED
                        )
                    }
                }
            } catch (e: IOException) {
                handler.post {
                    notifyError("断开连接失败: ${e.message}")
                }
            }
        }.start()

        return true
    }

    /**
     * 断开所有连接
     */
    fun disconnectAll() {
        val addresses = connectedDevices.keys.toList()
        addresses.forEach { address ->
            disconnectFromDevice(address)
        }
    }

    /**
     * 获取已连接的设备列表
     */
    fun getConnectedDevices(): List<BluetoothDeviceInfo> {
        return connectedDevices.keys.mapNotNull { address ->
            bluetoothAdapter?.getRemoteDevice(address)?.let { device ->
                BluetoothDeviceInfo.fromBluetoothDevice(device).copy(isConnected = true)
            }
        }
    }

    /**
     * 检查设备是否已连接
     */
    fun isDeviceConnected(deviceAddress: String): Boolean {
        return connectedDevices.containsKey(deviceAddress)
    }

    /**
     * 静默连接已配对的设备
     * 自动尝试连接所有已配对的设备
     */
    fun connectToPairedDevicesAutomatically(): Boolean {
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val pairedDevices = getPairedDevices()
        if (pairedDevices.isEmpty()) {
            notifyError("没有已配对的设备")
            return false
        }

        var hasStartedConnection = false
        pairedDevices.forEach { deviceInfo ->
            if (!isDeviceConnected(deviceInfo.address)) {
                connectToDevice(deviceInfo.address)
                hasStartedConnection = true
            }
        }

        return hasStartedConnection
    }

    /**
     * 静默连接指定的已配对设备
     */
    fun connectToPairedDeviceAutomatically(deviceAddress: String): Boolean {
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val pairedDevices = getPairedDevices()
        val targetDevice = pairedDevices.find { it.address == deviceAddress }

        if (targetDevice == null) {
            notifyError("设备未配对: $deviceAddress")
            return false
        }

        if (isDeviceConnected(deviceAddress)) {
            return true // 已连接
        }

        return connectToPairedDevice(deviceAddress)
    }

    /**
     * 连接已配对设备的专用方法
     * 对已配对设备使用更可靠的连接方式
     */
    fun connectToPairedDevice(deviceAddress: String): Boolean {
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        if (device == null) {
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        // 检查设备是否已配对
        if (device.bondState != BluetoothDevice.BOND_BONDED) {
            notifyError("设备未配对: $deviceAddress")
            return false
        }

        // 如果已经连接，直接返回成功
        if (connectedDevices.containsKey(deviceAddress)) {
            return true
        }

        val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device)
        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTING)

        // 在后台线程中执行连接
        Thread {
            connectToPairedDeviceInternal(device, deviceInfo, 0)
        }.start()

        return true
    }

    /**
     * 已配对设备的内部连接方法
     */
    private fun connectToPairedDeviceInternal(device: BluetoothDevice, deviceInfo: BluetoothDeviceInfo, retryCount: Int) {
        val maxRetries = 1 // 已配对设备减少重试次数

        try {
            // 停止扫描以提高连接成功率
            bluetoothAdapter?.cancelDiscovery()

            // 等待一下让扫描完全停止
            Thread.sleep(500)

            // 对于已配对设备，根据重试次数选择连接方法
            val socket = if (retryCount == 0) {
                // 第一次尝试：使用反射方法（对已配对设备更可靠）
                try {
                    val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                    method.invoke(device, 1) as BluetoothSocket
                } catch (e: Exception) {
                    // 如果反射失败，使用标准方法
                    device.createRfcommSocketToServiceRecord(SPP_UUID)
                }
            } else {
                // 重试时使用标准方法
                device.createRfcommSocketToServiceRecord(SPP_UUID)
            }

            // 设置连接超时
            val timeoutRunnable = Runnable {
                try {
                    socket.close()
                } catch (e: IOException) {
                    // 忽略关闭异常
                }
                handler.post {
                    if (retryCount < maxRetries) {
                        // 提示用户正在重试
                        notifyError("连接超时，正在重试...（${retryCount + 1}/${maxRetries + 1}）")
                        // 重试连接
                        Thread {
                            Thread.sleep(2000) // 等待2秒后重试
                            connectToPairedDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    } else {
                        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_TIMEOUT)
                        notifyError("连接超时。设备可能不在范围内、电量不足或已被其他设备连接")
                    }
                }
            }
            handler.postDelayed(timeoutRunnable, PAIRED_DEVICE_CONNECT_TIMEOUT)

            // 执行连接
            socket.connect()

            // 连接成功，取消超时
            handler.removeCallbacks(timeoutRunnable)

            // 保存连接
            connectedDevices[device.address] = socket

            handler.post {
                notifyConnectionStateChanged(
                    deviceInfo.copy(isConnected = true),
                    BluetoothConnectionState.CONNECTED
                )
                if (retryCount > 0) {
                    notifyError("连接成功！（第 ${retryCount + 1} 次尝试）")
                } else {
                    notifyError("连接成功！")
                }
            }

        } catch (e: IOException) {
            handler.post {
                if (retryCount < maxRetries) {
                    // 提示用户正在重试
                    notifyError("连接失败，正在重试...（${retryCount + 1}/${maxRetries + 1}）")
                    // 重试连接
                    Thread {
                        Thread.sleep(2000) // 等待2秒后重试
                        connectToPairedDeviceInternal(device, deviceInfo, retryCount + 1)
                    }.start()
                } else {
                    notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_FAILED)
                    notifyError("连接失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        stopScan()
        // 只有在BLE管理器已初始化时才清理
        bleManager?.let {
            it.stopBleScan()
            it.cleanup()
        }
        disconnectAll()
        callbacks.clear()
        INSTANCE = null
    }

    /**
     * 检测设备支持的服务
     */
    private fun detectDeviceServices(device: BluetoothDevice) {
        try {
            val uuids = device.uuids
            if (uuids != null && uuids.isNotEmpty()) {
                handler.post {
                    notifyError("设备支持的服务: ${uuids.joinToString { it.toString() }}")
                }
            } else {
                handler.post {
                    notifyError("无法获取设备服务信息，使用默认连接方法")
                }
            }
        } catch (e: Exception) {
            handler.post {
                notifyError("检测设备服务时出错: ${e.message}")
            }
        }
    }

    /**
     * 创建蓝牙Socket，尝试多种连接方法
     */
    private fun createBluetoothSocket(device: BluetoothDevice, retryCount: Int, isJustPaired: Boolean): BluetoothSocket? {
        val connectionMethods = listOf(
            "标准SPP方法" to { device.createRfcommSocketToServiceRecord(SPP_UUID) },
            "反射方法(端口1)" to {
                val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                method.invoke(device, 1) as BluetoothSocket
            },
            "反射方法(端口2)" to {
                val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                method.invoke(device, 2) as BluetoothSocket
            },
            "反射方法(端口3)" to {
                val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                method.invoke(device, 3) as BluetoothSocket
            }
        )

        // 根据情况选择优先的连接方法
        val orderedMethods = when {
            isJustPaired -> {
                // 刚配对的设备，优先使用反射方法
                listOf(1, 0, 2, 3)
            }
            retryCount == 0 -> {
                // 第一次尝试，使用标准方法
                listOf(0, 1, 2, 3)
            }
            else -> {
                // 重试时，优先使用反射方法
                listOf(1, 2, 3, 0)
            }
        }

        for (methodIndex in orderedMethods) {
            try {
                val (methodName, createSocket) = connectionMethods[methodIndex]
                handler.post {
                    notifyError("尝试连接方法: $methodName")
                }
                val socket = createSocket()
                if (socket != null) {
                    handler.post {
                        notifyError("成功创建Socket: $methodName")
                    }
                    return socket
                }
            } catch (e: Exception) {
                val (methodName, _) = connectionMethods[methodIndex]
                handler.post {
                    notifyError("连接方法失败: $methodName - ${e.message}")
                }
                // 继续尝试下一种方法
            }
        }

        // 所有方法都失败了
        handler.post {
            notifyError("所有连接方法都失败了")
        }
        return null
    }

    // 私有方法
    private fun registerReceiver() {
        if (!isReceiverRegistered) {
            val filter = IntentFilter().apply {
                addAction(BluetoothDevice.ACTION_FOUND)
                addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
                addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
                addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED) // 添加配对状态监听
                addAction(BluetoothDevice.ACTION_PAIRING_REQUEST) // 添加配对请求监听
            }
            context.registerReceiver(bluetoothReceiver, filter)
            isReceiverRegistered = true
        }
    }

    private fun unregisterReceiver() {
        if (isReceiverRegistered) {
            try {
                context.unregisterReceiver(bluetoothReceiver)
            } catch (e: IllegalArgumentException) {
                // 接收器可能已经被注销
            }
            isReceiverRegistered = false
        }
    }

    private fun startScanTimeout() {
        stopScanTimeout()
        scanTimeoutRunnable = Runnable {
            bluetoothAdapter?.cancelDiscovery()
            updateScanState(BluetoothScanState.SCAN_FINISHED)
            unregisterReceiver()
        }
        handler.postDelayed(scanTimeoutRunnable!!, SCAN_TIMEOUT)
    }

    private fun stopScanTimeout() {
        scanTimeoutRunnable?.let { runnable ->
            handler.removeCallbacks(runnable)
            scanTimeoutRunnable = null
        }
    }

    private fun updateScanState(state: BluetoothScanState) {
        currentScanState = state
        notifyScanStateChanged(state)
    }

    private fun notifyScanStateChanged(state: BluetoothScanState) {
        callbacks.forEach { it.onScanStateChanged(state) }
    }

    private fun notifyDeviceFound(device: BluetoothDeviceInfo) {
        callbacks.forEach { it.onDeviceFound(device) }
    }

    private fun notifyConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {
        callbacks.forEach { it.onConnectionStateChanged(device, state) }
    }

    private fun notifyError(error: String) {
        callbacks.forEach { it.onError(error) }
    }

    private fun notifySuccess(message: String) {
        callbacks.forEach { it.onSuccess(message) }
    }

    private fun notifyInfo(message: String) {
        callbacks.forEach { it.onInfo(message) }
    }

    private fun notifyDataReceived(senderAddress: String, data: String) {
        callbacks.forEach { it.onDataReceived(senderAddress, data) }
    }

    // ==================== 蓝牙服务器功能 ====================

    /**
     * 启动统一蓝牙服务器，同时支持经典蓝牙和BLE消息接收
     * 这个方法会：
     * 1. 启动应用层蓝牙广播（设置应用专用名称）
     * 2. 启动经典蓝牙服务器（SPP协议）
     * 3. 启动BLE扫描和连接管理
     * 4. 统一处理所有蓝牙消息
     */
    fun startUnifiedBluetoothServer(appBluetoothName: String? = null): Boolean {
        Log.d(TAG, "启动统一蓝牙服务器...")

        // 启动应用层蓝牙广播（如果提供了应用名称）
        var broadcastResult = true
        appBluetoothName?.let { name ->
            broadcastResult = startAppBluetoothBroadcast(name)
            if (broadcastResult) {
                Log.i(TAG, "应用蓝牙广播启动成功：$name")
            } else {
                Log.w(TAG, "应用蓝牙广播启动失败，继续启动服务器")
            }
        }

        // 启动经典蓝牙服务器
        val classicResult = startBluetoothServer()

        // 启动BLE功能
        val bleResult = startBleServer()

        if (classicResult || bleResult) {
            val message = if (appBluetoothName != null) {
                "统一蓝牙服务器启动成功 - 广播: $broadcastResult, 经典蓝牙: $classicResult, BLE: $bleResult"
            } else {
                "统一蓝牙服务器启动成功 - 经典蓝牙: $classicResult, BLE: $bleResult"
            }
            notifySuccess(message)
            return true
        } else {
            notifyError("统一蓝牙服务器启动失败")
            return false
        }
    }

    /**
     * 停止统一蓝牙服务器
     */
    fun stopUnifiedBluetoothServer() {
        Log.d(TAG, "停止统一蓝牙服务器...")

        // 停止蓝牙广播
        stopAppBluetoothBroadcast()

        // 停止经典蓝牙服务器
        stopBluetoothServer()

        // 停止BLE服务器
        stopBleServer()

        notifyInfo("统一蓝牙服务器已停止")
    }

    /**
     * 启动BLE服务器功能
     */
    private fun startBleServer(): Boolean {
        return try {
            // 启动BLE扫描以发现设备
            getBleManager().startBleScan()

            // 自动连接已配对的BLE设备（特别是点云灵动键）
            handler.postDelayed({
                autoConnectPairedBleDevices()
            }, 2000) // 等待2秒后开始自动连接

            Log.d(TAG, "BLE服务器功能启动成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "BLE服务器功能启动失败: ${e.message}", e)
            false
        }
    }

    /**
     * 停止BLE服务器功能
     */
    private fun stopBleServer() {
        try {
            getBleManager().stopBleScan()
            // 断开所有BLE连接
            val connectedDevices = getBleManager().getConnectedBleDevices()
            connectedDevices.forEach { deviceAddress ->
                getBleManager().disconnectBleDevice(deviceAddress)
            }
            Log.d(TAG, "BLE服务器功能已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止BLE服务器功能时出错: ${e.message}", e)
        }
    }

    /**
     * 自动连接已配对的BLE设备
     * 特别针对点云灵动键等按键设备
     */
    private fun autoConnectPairedBleDevices() {
        Log.d(TAG, "开始自动连接已配对的BLE设备...")

        try {
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            if (bluetoothAdapter == null) {
                Log.e(TAG, "蓝牙适配器不可用")
                return
            }

            val pairedDevices = bluetoothAdapter.bondedDevices
            var bleDeviceCount = 0

            pairedDevices?.forEach { device ->
                // BLE设备通常类型为DEVICE_TYPE_LE或DEVICE_TYPE_DUAL
                if (device.type == BluetoothDevice.DEVICE_TYPE_LE ||
                    device.type == BluetoothDevice.DEVICE_TYPE_DUAL) {

                    Log.d(TAG, "发现已配对的BLE设备: ${device.name} (${device.address})")

                    // 自动连接BLE设备
                    handler.postDelayed({
                        connectToBleDevice(device.address)
                    }, bleDeviceCount * 1000L) // 每个设备间隔1秒连接

                    bleDeviceCount++
                }
            }

            if (bleDeviceCount > 0) {
                Log.d(TAG, "找到 $bleDeviceCount 个已配对的BLE设备，开始自动连接")
                notifyInfo("找到 $bleDeviceCount 个已配对的BLE设备，正在自动连接...")
            } else {
                Log.d(TAG, "没有找到已配对的BLE设备")
                notifyInfo("没有找到已配对的BLE设备，请先配对点云灵动键")
            }

        } catch (e: Exception) {
            Log.e(TAG, "自动连接BLE设备失败: ${e.message}", e)
        }
    }

    /**
     * 启动蓝牙服务器，监听来自其他设备的连接
     */
    fun startBluetoothServer(): Boolean {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        if (isServerRunning) {
            notifyInfo("蓝牙服务器已在运行")
            return false
        }

        try {
            bluetoothServerSocket = bluetoothAdapter?.listenUsingRfcommWithServiceRecord(
                "BluetoothChatServer",
                SPP_UUID
            )

            if (bluetoothServerSocket == null) {
                notifyError("无法创建蓝牙服务器Socket")
                return false
            }

            isServerRunning = true

            serverThread = Thread {
                acceptConnections()
            }.apply {
                name = "BluetoothServerThread"
                start()
            }

            // 服务器启动成功，不需要通过错误回调通知
            // UI会通过isServerRunning()状态来显示正确信息

            return true

        } catch (e: Exception) {
            handler.post {
                notifyError("启动蓝牙服务器失败: ${e.message}")
            }
            return false
        }
    }

    /**
     * 停止蓝牙服务器
     */
    fun stopBluetoothServer() {
        isServerRunning = false

        try {
            bluetoothServerSocket?.close()
            bluetoothServerSocket = null

            serverThread?.interrupt()
            serverThread = null

            handler.post {
                notifyInfo("蓝牙服务器已停止")
            }

        } catch (e: Exception) {
            handler.post {
                notifyError("停止蓝牙服务器时出错: ${e.message}")
            }
        }
    }

    /**
     * 检查服务器是否正在运行
     */
    fun isServerRunning(): Boolean {
        return isServerRunning
    }

    /**
     * 接受客户端连接的循环
     */
    private fun acceptConnections() {
        while (isServerRunning && bluetoothServerSocket != null) {
            try {
                val clientSocket = bluetoothServerSocket?.accept()

                if (clientSocket != null && isServerRunning) {
                    // 处理客户端连接
                    Thread {
                        handleClientConnection(clientSocket)
                    }.apply {
                        name = "ClientHandler-${clientSocket.remoteDevice.address}"
                        start()
                    }
                }

            } catch (e: Exception) {
                if (isServerRunning) {
                    handler.post {
                        notifyError("接受连接时出错: ${e.message}")
                    }
                }
                break
            }
        }
    }

    /**
     * 处理单个客户端连接
     */
    private fun handleClientConnection(socket: BluetoothSocket) {
        val device = socket.remoteDevice
        val deviceAddress = device.address

        handler.post {
            notifyError("客户端已连接: ${device.name ?: deviceAddress}")
        }

        try {
            val inputStream = socket.inputStream
            val buffer = ByteArray(1024)

            while (isServerRunning && socket.isConnected) {
                try {
                    val bytesRead = inputStream.read(buffer)
                    if (bytesRead > 0) {
                        val receivedData = String(buffer, 0, bytesRead, Charsets.UTF_8)

                        handler.post {
                            notifyInfo("收到来自 ${device.name ?: deviceAddress} 的数据: \"$receivedData\"")
                            notifyDataReceived(deviceAddress, receivedData)
                        }

                        // 可选：发送确认响应
                        try {
                            val outputStream = socket.outputStream
                            val response = "收到: $receivedData"
                            outputStream.write(response.toByteArray(Charsets.UTF_8))
                            outputStream.flush()
                        } catch (e: Exception) {
                            // 发送响应失败，但不影响接收
                        }
                    }
                } catch (e: Exception) {
                    // 读取数据失败，可能是连接断开
                    break
                }
            }

        } catch (e: Exception) {
            handler.post {
                notifyError("处理客户端连接时出错: ${e.message}")
            }
        } finally {
            try {
                socket.close()
                handler.post {
                    notifyError("客户端连接已断开: ${device.name ?: deviceAddress}")
                }
            } catch (e: Exception) {
                // 忽略关闭时的错误
            }
        }
    }

    private fun notifyPermissionRequired(permissions: Array<String>) {
        callbacks.forEach { it.onPermissionRequired(permissions) }
    }

    // ==================== BLE功能 ====================

    /**
     * 获取BLE管理器实例，延迟初始化
     */
    private fun getBleManager(): BleManager {
        if (bleManager == null) {
            bleManager = BleManager.getInstance(context)
            setupBleManagerCallbacks()
        }
        return bleManager!!
    }

    /**
     * 设置BLE管理器回调
     */
    private fun setupBleManagerCallbacks() {
        bleManager!!.addCallback(object : BluetoothCallback {
            override fun onDeviceFound(device: BluetoothDeviceInfo) {
                // 转发BLE设备发现事件
                notifyDeviceFound(device)
            }

            override fun onError(error: String) {
                // 转发BLE错误事件
                notifyError(error)
            }

            override fun onSuccess(message: String) {
                // 转发BLE成功事件
                notifySuccess(message)
            }

            override fun onInfo(message: String) {
                // 转发BLE信息事件
                notifyInfo(message)
            }

            override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
                // 转发BLE特征值通知事件
                callbacks.forEach { it.onCharacteristicNotification(deviceAddress, characteristicUuid, data) }
            }

            override fun onBleConnectionStateChanged(deviceAddress: String, state: Int) {
                // 转发BLE连接状态变化事件
                callbacks.forEach { it.onBleConnectionStateChanged(deviceAddress, state) }

                // 如果是连接成功，自动订阅0xFFE4特征值
                if (state == 2) { // BluetoothProfile.STATE_CONNECTED
                    Log.d(TAG, "BLE设备连接成功，自动订阅0xFFE4特征值: $deviceAddress")
                    // 延迟一点时间确保服务发现完成
                    handler.postDelayed({
                        subscribeToFFE4Notification(deviceAddress)
                    }, 1000)
                }
            }
        })
    }

    /**
     * 开始BLE扫描
     */
    fun startBleScan(): Boolean {
        return getBleManager().startBleScan()
    }

    /**
     * 停止BLE扫描
     */
    fun stopBleScan() {
        getBleManager().stopBleScan()
    }

    /**
     * 连接到BLE设备
     */
    fun connectToBleDevice(deviceAddress: String): Boolean {
        return getBleManager().connectToBleDeviceWithPreparation(deviceAddress)
    }

    /**
     * 订阅0xFFE4特征值的Notify通知
     */
    fun subscribeToFFE4Notification(deviceAddress: String): Boolean {
        val ffe4Uuid = java.util.UUID.fromString("0000FFE4-0000-1000-8000-00805F9B34FB")
        return getBleManager().subscribeToCharacteristic(deviceAddress, ffe4Uuid)
    }

    /**
     * 订阅指定特征值的Notify通知
     */
    fun subscribeToCharacteristic(deviceAddress: String, characteristicUuid: java.util.UUID): Boolean {
        return getBleManager().subscribeToCharacteristic(deviceAddress, characteristicUuid)
    }

    /**
     * 断开BLE设备连接
     */
    fun disconnectBleDevice(deviceAddress: String) {
        getBleManager().disconnectBleDevice(deviceAddress)
    }

    /**
     * 获取已连接的BLE设备列表
     */
    fun getConnectedBleDevices(): List<String> {
        return getBleManager().getConnectedBleDevices()
    }

    /**
     * 自动连接到指定的BLE设备并订阅0xFFE4特征值
     * 这个方法通常在统一蓝牙服务器中使用
     */
    fun autoConnectAndSubscribeBleDevice(deviceAddress: String): Boolean {
        Log.d(TAG, "自动连接并订阅BLE设备: $deviceAddress")

        // 先尝试连接
        val connected = connectToBleDevice(deviceAddress)
        if (connected) {
            // 连接成功后，订阅会在连接状态回调中自动处理
            Log.d(TAG, "BLE设备连接请求已发送，等待连接完成后自动订阅")
            return true
        } else {
            Log.e(TAG, "BLE设备连接失败: $deviceAddress")
            return false
        }
    }

    /**
     * 批量连接多个BLE设备
     */
    fun autoConnectMultipleBleDevices(deviceAddresses: List<String>) {
        Log.d(TAG, "批量连接BLE设备: ${deviceAddresses.size}个设备")
        deviceAddresses.forEach { address ->
            autoConnectAndSubscribeBleDevice(address)
        }
    }

    /**
     * 诊断BLE设备的详细信息
     * 用于排查BLE设备兼容性问题
     */
    fun diagnoseBleDevice(deviceAddress: String): Boolean {
        return getBleManager().diagnoseBleDevice(deviceAddress)
    }

    /**
     * 诊断BLE连接问题
     * 用于排查连接失败的原因
     */
    fun diagnoseBleConnectionIssues(deviceAddress: String) {
        getBleManager().diagnoseConnectionIssues(deviceAddress)
    }

    // ==================== 外设控制功能 ====================

    /**
     * 向BLE设备的特征值写入数据
     */
    fun writeCharacteristic(deviceAddress: String, characteristicUuid: java.util.UUID, data: ByteArray): Boolean {
        return getBleManager().writeCharacteristic(deviceAddress, characteristicUuid, data)
    }

    /**
     * 发送外设控制指令
     */
    fun sendPeripheralCommand(deviceAddress: String, deviceType: Byte, command: Byte): Boolean {
        return getBleManager().sendPeripheralCommand(deviceAddress, deviceType, command)
    }

    /**
     * 控制无线充电器
     */
    fun controlWirelessCharger(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlWirelessCharger(deviceAddress, enable)
    }

    /**
     * 控制主灯
     */
    fun controlMainLamp(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlMainLamp(deviceAddress, enable)
    }

    /**
     * 控制氛围灯
     */
    fun controlAmbientLight(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlAmbientLight(deviceAddress, enable)
    }

    /**
     * 控制香氛机
     */
    fun controlAromaDiffuser(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlAromaDiffuser(deviceAddress, enable)
    }

    /**
     * 控制风扇
     */
    fun controlFan(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlFan(deviceAddress, enable)
    }

    /**
     * 查询外设状态
     */
    fun queryPeripheralStatus(deviceAddress: String, deviceType: Byte): Boolean {
        return getBleManager().queryPeripheralStatus(deviceAddress, deviceType)
    }

    // ==================== 无感连接功能 ====================

    /**
     * 尝试无感连接（需要系统级权限）
     * 使用反射调用系统级API实现自动配对
     */
    private fun attemptSeamlessConnection(device: BluetoothDevice, deviceInfo: BluetoothDeviceInfo): Boolean {
        return try {
            notifyError("正在尝试无感连接...")

            // 方法1：尝试使用反射调用隐藏的配对方法
            val success = tryAutoPairing(device)

            if (success) {
                notifyError("无感连接成功！")
                connectingDevices.remove(device.address)
                handler.post {
                    notifyConnectionStateChanged(deviceInfo.copy(isPaired = true), BluetoothConnectionState.CONNECTED)
                    notifySuccess("设备已自动配对并连接")
                }
                true
            } else {
                // 如果无感连接失败，回退到传统配对方式
                notifyError("无感连接失败，回退到传统配对方式...")
                fallbackToTraditionalPairing(device, deviceInfo)
            }
        } catch (e: Exception) {
            Log.e(TAG, "无感连接异常: ${e.message}", e)
            notifyError("无感连接异常，回退到传统配对方式...")
            fallbackToTraditionalPairing(device, deviceInfo)
        }
    }

    /**
     * 尝试自动配对（使用反射调用系统API）
     */
    private fun tryAutoPairing(device: BluetoothDevice): Boolean {
        return try {
            // 方法1：尝试设置配对确认
            val setPairingConfirmationMethod = device.javaClass.getMethod("setPairingConfirmation", Boolean::class.javaPrimitiveType)
            val confirmResult = setPairingConfirmationMethod.invoke(device, true) as Boolean

            if (confirmResult) {
                Log.d(TAG, "自动配对确认成功")

                // 方法2：尝试创建配对
                val createBondMethod = device.javaClass.getMethod("createBond")
                val bondResult = createBondMethod.invoke(device) as Boolean

                if (bondResult) {
                    Log.d(TAG, "自动创建配对成功")
                    // 等待配对完成
                    Thread.sleep(3000)
                    return device.bondState == BluetoothDevice.BOND_BONDED
                }
            }

            // 方法3：尝试直接设置PIN码（如果设备支持）
            trySetPin(device)

        } catch (e: Exception) {
            Log.e(TAG, "自动配对失败: ${e.message}", e)
            false
        }
    }

    /**
     * 尝试设置PIN码进行自动配对
     */
    private fun trySetPin(device: BluetoothDevice): Boolean {
        return try {
            // 常用的默认PIN码列表
            val commonPins = arrayOf("0000", "1234", "1111", "0001", "1122")

            for (pin in commonPins) {
                try {
                    val setPinMethod = device.javaClass.getMethod("setPin", ByteArray::class.java)
                    val pinResult = setPinMethod.invoke(device, pin.toByteArray()) as Boolean

                    if (pinResult) {
                        Log.d(TAG, "PIN码设置成功: $pin")
                        Thread.sleep(2000) // 等待配对完成

                        if (device.bondState == BluetoothDevice.BOND_BONDED) {
                            Log.d(TAG, "PIN码配对成功")
                            return true
                        }
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "PIN码 $pin 配对失败: ${e.message}")
                    continue
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "PIN码配对异常: ${e.message}", e)
            false
        }
    }

    /**
     * 回退到传统配对方式
     */
    private fun fallbackToTraditionalPairing(device: BluetoothDevice, deviceInfo: BluetoothDeviceInfo): Boolean {
        return try {
            notifyError("开始传统配对，请在目标设备上确认配对请求...")
            val paired = device.createBond()

            if (!paired) {
                connectingDevices.remove(device.address)
                notifyError("无法启动配对过程")
                notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.DISCONNECTED)
                false
            } else {
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "传统配对失败: ${e.message}", e)
            connectingDevices.remove(device.address)
            notifyError("配对失败: ${e.message}")
            notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.DISCONNECTED)
            false
        }
    }

    /**
     * 批量无感连接多个设备
     */
    fun connectToDevicesSeamlessly(deviceAddresses: List<String>): Boolean {
        if (!supportsSeamlessConnection()) {
            notifyError("当前环境不支持无感连接，需要系统级权限")
            return false
        }

        var successCount = 0
        deviceAddresses.forEach { address ->
            if (connectToDevice(address)) {
                successCount++
            }
        }

        notifyInfo("批量无感连接完成：成功 $successCount/${deviceAddresses.size} 个设备")
        return successCount > 0
    }

    // ==================== 蓝牙广播功能 ====================

    /**
     * 启动应用层蓝牙广播
     * 设置应用专用的蓝牙名称，使手机能够发现并连接到我们的应用
     */
    fun startAppBluetoothBroadcast(appName: String): Boolean {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        if (!isBluetoothEnabled()) {
            notifyError("蓝牙未启用，无法启动广播")
            return false
        }

        if (isBroadcasting) {
            notifyInfo("蓝牙广播已在运行")
            return true
        }

        try {
            // 保存原始蓝牙名称
            originalBluetoothName = bluetoothAdapter?.name

            // 设置应用专用的蓝牙名称
            val success = bluetoothAdapter?.setName(appName) ?: false
            if (!success) {
                notifyError("设置蓝牙名称失败")
                return false
            }

            // 启用蓝牙可发现性
            val discoverableIntent = Intent(BluetoothAdapter.ACTION_REQUEST_DISCOVERABLE).apply {
                putExtra(BluetoothAdapter.EXTRA_DISCOVERABLE_DURATION, 300) // 5分钟可发现
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            try {
                context.startActivity(discoverableIntent)
            } catch (e: Exception) {
                Log.w(TAG, "无法启动可发现性请求，尝试直接设置: ${e.message}")
                // 如果有系统级权限，尝试直接设置可发现性
                if (supportsSeamlessConnection()) {
                    setDiscoverableDirectly(300)
                }
            }

            isBroadcasting = true

            // 启用主动配对模式（避免手机端配对确认窗口）
            enableProactivePairing(true)

            // 启动定期广播刷新
            startBroadcastTimer(appName)

            notifySuccess("应用蓝牙广播已启动：$appName（已启用无感配对）")
            Log.i(TAG, "应用蓝牙广播已启动，设备名称：$appName，主动配对已启用")

            return true

        } catch (e: Exception) {
            Log.e(TAG, "启动蓝牙广播失败: ${e.message}", e)
            notifyError("启动蓝牙广播失败: ${e.message}")
            return false
        }
    }

    /**
     * 停止应用层蓝牙广播
     */
    fun stopAppBluetoothBroadcast(): Boolean {
        if (!isBroadcasting) {
            return true
        }

        try {
            // 停止广播定时器
            stopBroadcastTimer()

            // 禁用主动配对模式
            enableProactivePairing(false)

            // 恢复原始蓝牙名称
            originalBluetoothName?.let { originalName ->
                bluetoothAdapter?.setName(originalName)
                Log.i(TAG, "已恢复原始蓝牙名称：$originalName")
            }

            isBroadcasting = false
            notifyInfo("应用蓝牙广播已停止，主动配对已禁用")

            return true

        } catch (e: Exception) {
            Log.e(TAG, "停止蓝牙广播失败: ${e.message}", e)
            notifyError("停止蓝牙广播失败: ${e.message}")
            return false
        }
    }

    /**
     * 检查是否正在广播
     */
    fun isBroadcasting(): Boolean {
        return isBroadcasting
    }

    /**
     * 获取当前蓝牙设备名称
     */
    fun getCurrentBluetoothName(): String? {
        return bluetoothAdapter?.name
    }

    /**
     * 启动广播定时器，定期刷新可发现性
     */
    private fun startBroadcastTimer(appName: String) {
        stopBroadcastTimer() // 先停止现有的定时器

        broadcastTimer = Timer("BluetoothBroadcastTimer", true)
        broadcastTimer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                try {
                    // 定期刷新蓝牙名称和可发现性
                    bluetoothAdapter?.setName(appName)

                    // 如果有系统级权限，直接设置可发现性
                    if (supportsSeamlessConnection()) {
                        setDiscoverableDirectly(300)
                    }

                    Log.d(TAG, "蓝牙广播刷新：$appName")
                } catch (e: Exception) {
                    Log.e(TAG, "广播定时器执行失败: ${e.message}", e)
                }
            }
        }, 30000L, 30000L) // 30秒后开始，每30秒执行一次
    }

    /**
     * 停止广播定时器
     */
    private fun stopBroadcastTimer() {
        broadcastTimer?.cancel()
        broadcastTimer = null
    }

    /**
     * 直接设置蓝牙可发现性（需要系统级权限）
     */
    private fun setDiscoverableDirectly(duration: Int): Boolean {
        return try {
            if (!supportsSeamlessConnection()) {
                return false
            }

            // 使用反射调用系统API设置可发现性
            val setScanModeMethod = bluetoothAdapter?.javaClass?.getMethod(
                "setScanMode",
                Int::class.javaPrimitiveType,
                Int::class.javaPrimitiveType
            )

            val result = setScanModeMethod?.invoke(
                bluetoothAdapter,
                BluetoothAdapter.SCAN_MODE_CONNECTABLE_DISCOVERABLE,
                duration
            ) as? Boolean ?: false

            if (result) {
                Log.d(TAG, "直接设置可发现性成功，持续时间：${duration}秒")
            } else {
                Log.w(TAG, "直接设置可发现性失败")
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "直接设置可发现性异常: ${e.message}", e)
            false
        }
    }

    // ==================== 主动配对功能 ====================

    /**
     * 启用主动配对模式
     * 在此模式下，树莓派会主动处理来自手机的配对请求，避免手机端弹出配对确认窗口
     */
    fun enableProactivePairing(enable: Boolean) {
        enableProactivePairing = enable
        Log.i(TAG, "主动配对模式: ${if (enable) "已启用" else "已禁用"}")

        if (enable && !supportsSeamlessConnection()) {
            Log.w(TAG, "警告：主动配对需要系统级权限才能完全生效")
            notifyError("主动配对需要系统级权限，当前可能无法完全避免手机端配对确认")
        }
    }

    /**
     * 检查是否启用了主动配对
     */
    fun isProactivePairingEnabled(): Boolean {
        return enableProactivePairing
    }

    /**
     * 处理传入的配对请求
     */
    private fun handleIncomingPairingRequest(device: BluetoothDevice, intent: Intent) {
        try {
            val pairingVariant = intent.getIntExtra(BluetoothDevice.EXTRA_PAIRING_VARIANT, BluetoothDevice.ERROR)
            Log.d(TAG, "处理配对请求: ${device.name} (${device.address}), 配对类型: $pairingVariant")

            when (pairingVariant) {
                0, 1 -> { // PIN或密钥配对
                    val pin = intent.getIntExtra(BluetoothDevice.EXTRA_PAIRING_KEY, 0)
                    Log.d(TAG, "自动确认PIN配对: $pin")
                    autoConfirmPairing(device, pin.toString())
                }

                2 -> { // 同意配对
                    Log.d(TAG, "自动同意配对请求")
                    autoConfirmPairing(device, null)
                }

                3, 4 -> { // 显示密钥配对
                    val key = intent.getIntExtra(BluetoothDevice.EXTRA_PAIRING_KEY, 0)
                    Log.d(TAG, "自动确认显示密钥配对: $key")
                    autoConfirmPairing(device, key.toString())
                }

                else -> {
                    Log.d(TAG, "未知配对类型($pairingVariant)，尝试通用确认")
                    autoConfirmPairing(device, null)
                }
            }

            pendingPairings.add(device.address)

        } catch (e: Exception) {
            Log.e(TAG, "处理配对请求失败: ${e.message}", e)
        }
    }

    /**
     * 自动确认配对
     */
    private fun autoConfirmPairing(device: BluetoothDevice, pin: String?) {
        try {
            // 方法1：使用setPairingConfirmation
            val setPairingConfirmationMethod = device.javaClass.getMethod("setPairingConfirmation", Boolean::class.javaPrimitiveType)
            val confirmResult = setPairingConfirmationMethod.invoke(device, true) as? Boolean ?: false

            if (confirmResult) {
                Log.d(TAG, "配对确认成功: ${device.address}")
            } else {
                Log.w(TAG, "配对确认失败，尝试其他方法")

                // 方法2：如果有PIN码，尝试设置PIN
                pin?.let { pinCode ->
                    try {
                        val setPinMethod = device.javaClass.getMethod("setPin", ByteArray::class.java)
                        val pinResult = setPinMethod.invoke(device, pinCode.toByteArray()) as? Boolean ?: false
                        Log.d(TAG, "设置PIN结果: $pinResult")
                    } catch (e: Exception) {
                        Log.w(TAG, "设置PIN失败: ${e.message}")
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "自动确认配对异常: ${e.message}", e)
        }
    }

    /**
     * 尝试自动确认配对（用于配对过程中）
     */
    private fun tryAutoConfirmPairing(device: BluetoothDevice) {
        try {
            Log.d(TAG, "尝试自动确认配对: ${device.address}")

            // 使用反射调用隐藏的配对确认方法
            val setPairingConfirmationMethod = device.javaClass.getMethod("setPairingConfirmation", Boolean::class.javaPrimitiveType)
            val result = setPairingConfirmationMethod.invoke(device, true) as? Boolean ?: false

            if (result) {
                Log.i(TAG, "自动配对确认成功: ${device.address}")
            } else {
                Log.w(TAG, "自动配对确认失败: ${device.address}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "自动配对确认异常: ${e.message}", e)
        }
    }
}
