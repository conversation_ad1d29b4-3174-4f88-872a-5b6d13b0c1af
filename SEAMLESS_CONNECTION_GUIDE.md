# 蓝牙无感连接功能使用指南

## 概述

本应用现已支持蓝牙无感连接功能，可以在树莓派环境下实现自动配对和连接，无需用户手动确认配对请求。

## 功能特性

### ✅ 已实现的功能

1. **系统级权限检测**
   - 自动检测是否具有 `BLUETOOTH_PRIVILEGED` 权限
   - 在UI中显示无感连接支持状态

2. **无感配对**
   - 自动配对确认，无需用户手动点击确认
   - 支持常用PIN码自动尝试（0000, 1234, 1111等）
   - 智能回退到传统配对方式

3. **批量无感连接**
   - 支持同时连接多个设备
   - 自动重试机制

4. **UI增强**
   - 配对按钮显示"无感配对"标识
   - 权限状态实时显示
   - 绿色按钮表示支持无感连接

## 安装要求

### 系统要求
- ✅ 树莓派系统（已root）
- ✅ Android系统
- ✅ 蓝牙硬件支持

### 权限要求
- `android.permission.BLUETOOTH_PRIVILEGED` - 系统级蓝牙权限
- `android.permission.BLUETOOTH_STACK` - 蓝牙协议栈权限
- `android.permission.BLUETOOTH_MAP` - 蓝牙消息访问权限

## 安装步骤

### 方法一：使用自动安装脚本（推荐）

1. **编译应用**
   ```bash
   ./gradlew assembleDebug
   ```

2. **运行安装脚本**
   ```bash
   chmod +x install_as_system_app.sh
   sudo ./install_as_system_app.sh
   ```

3. **重启设备**
   ```bash
   sudo reboot
   ```

### 方法二：手动安装

1. **编译应用**
   ```bash
   ./gradlew assembleDebug
   ```

2. **挂载系统分区为可写**
   ```bash
   sudo mount -o remount,rw /system
   ```

3. **创建系统应用目录**
   ```bash
   sudo mkdir -p /system/priv-app/ExhibitionCarControl
   ```

4. **复制APK文件**
   ```bash
   sudo cp app/build/outputs/apk/debug/app-debug.apk /system/priv-app/ExhibitionCarControl/ExhibitionCarControl.apk
   ```

5. **设置权限**
   ```bash
   sudo chmod 644 /system/priv-app/ExhibitionCarControl/ExhibitionCarControl.apk
   sudo chown root:root /system/priv-app/ExhibitionCarControl/ExhibitionCarControl.apk
   ```

6. **创建权限配置文件**
   ```bash
   sudo tee /system/etc/permissions/com.example.exhibition_car_control.xml > /dev/null << EOF
   <?xml version="1.0" encoding="utf-8"?>
   <permissions>
       <privapp-permissions package="com.example.exhibition_car_control">
           <permission name="android.permission.BLUETOOTH_PRIVILEGED"/>
           <permission name="android.permission.BLUETOOTH_STACK"/>
           <permission name="android.permission.BLUETOOTH_MAP"/>
       </privapp-permissions>
   </permissions>
   EOF
   ```

7. **重新挂载为只读并重启**
   ```bash
   sudo mount -o remount,ro /system
   sudo reboot
   ```

## 使用方法

### 1. 检查无感连接支持状态

启动应用后，在"静默连接控制"卡片中查看：
- ✅ 绿色提示：系统级权限已获得，支持无感连接
- ⚠️ 橙色提示：使用传统配对方式，需要手动确认

### 2. 无感配对新设备

1. 点击"开始扫描"发现附近设备
2. 对于未配对设备，点击绿色的"无感配对"按钮
3. 应用将自动尝试配对，无需手动确认

### 3. 批量无感连接

在"静默连接控制"区域：
1. 点击"批量无感连接"按钮
2. 应用将尝试连接预设的设备列表
3. 查看连接结果反馈

### 4. 自动连接已配对设备

- 点击"连接所有已配对设备"
- 应用将自动连接所有已配对的设备

## 技术原理

### 无感连接实现机制

1. **权限检查**
   ```kotlin
   fun supportsSeamlessConnection(): Boolean {
       return hasPrivilegedBluetoothPermissions()
   }
   ```

2. **自动配对确认**
   ```kotlin
   val setPairingConfirmationMethod = device.javaClass.getMethod("setPairingConfirmation", Boolean::class.javaPrimitiveType)
   setPairingConfirmationMethod.invoke(device, true)
   ```

3. **PIN码自动尝试**
   ```kotlin
   val commonPins = arrayOf("0000", "1234", "1111", "0001", "1122")
   // 自动尝试常用PIN码
   ```

4. **智能回退**
   - 如果无感连接失败，自动回退到传统配对方式
   - 确保兼容性和可靠性

## 故障排除

### 常见问题

1. **权限未生效**
   - 确保设备已重启
   - 检查权限配置文件是否正确创建
   - 验证应用是否安装在 `/system/priv-app/` 目录

2. **无感连接失败**
   - 检查目标设备是否支持自动配对
   - 查看日志输出获取详细错误信息
   - 尝试传统配对方式作为备选

3. **系统分区只读**
   ```bash
   sudo mount -o remount,rw /system
   ```

### 验证安装

1. **检查应用是否为系统应用**
   ```bash
   pm list packages -s | grep com.example.exhibition_car_control
   ```

2. **检查权限是否获得**
   ```bash
   dumpsys package com.example.exhibition_car_control | grep BLUETOOTH_PRIVILEGED
   ```

3. **查看应用日志**
   ```bash
   adb logcat | grep BluetoothManager
   ```

## 安全注意事项

1. **系统级权限风险**
   - 系统级权限具有较高的安全风险
   - 仅在可信环境中使用
   - 定期检查应用行为

2. **自动配对风险**
   - 无感连接可能连接到恶意设备
   - 建议在受控环境中使用
   - 监控连接的设备列表

## 卸载方法

如需卸载系统应用：

```bash
sudo mount -o remount,rw /system
sudo rm -rf /system/priv-app/ExhibitionCarControl
sudo rm -f /system/etc/permissions/com.example.exhibition_car_control.xml
sudo mount -o remount,ro /system
sudo reboot
```

## 支持的设备类型

- 📱 手机和平板设备
- 🔌 充电器设备 (SMART-WC)
- 💡 主灯设备 (SMART-ML)
- 🌈 氛围灯设备 (SMART-AL)
- 🌸 香薰设备 (SMART-DF)
- 🌀 风扇设备 (SMART-FN)
- 🎮 蓝牙按键设备 (Dotix)

## 更新日志

### v1.0.0
- ✅ 添加BLUETOOTH_PRIVILEGED权限支持
- ✅ 实现无感配对功能
- ✅ 添加批量连接功能
- ✅ 优化UI显示权限状态
- ✅ 添加自动安装脚本
